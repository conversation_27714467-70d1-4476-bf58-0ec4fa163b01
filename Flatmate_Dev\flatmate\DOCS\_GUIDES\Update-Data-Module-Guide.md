# Update Data Module - Developer Onboarding Guide

**Last Updated**: Thursday, August 1, 2025 @ 9:50:00 PM
**Module Version**: Decomposed Architecture (Post-Refactoring)  
**Target Audience**: AI Agents & Developers

## Overview

The Update Data module handles financial statement processing and database updates. It has been refactored from a monolithic presenter into a decomposed architecture with specialized managers.

## Architecture Summary

### Core Pattern: Decomposed MVP
- **Model**: Database services, file processing pipeline
- **View**: Component-based UI with interface abstraction  
- **Presenter**: Coordinator + 4 specialized managers

### Key Principle: Single Responsibility
Each manager handles one specific concern, enabling independent testing and maintenance.

## Directory Structure

```
update_data/
├── __init__.py                    # Module exports
├── ud_presenter.py               # Main coordinator
├── ud_view.py                    # View implementation
├── interface/                    # View abstraction layer
│   └── i_view_interface.py      # IUpdateDataView protocol
├── _presenter/                   # Decomposed managers
│   ├── state_manager.py         # Consolidated state + UI sync
│   ├── file_manager.py          # File/folder selection + save location
│   ├── file_list_manager.py     # Canonical file list management
│   └── processing_manager.py    # File processing
├── _view/                       # UI components
│   ├── center_panel.py          # Main content area
│   └── left_panel.py            # Actions/options
├── config/                      # Module configuration
│   ├── ud_config.py            # Config wrapper
│   ├── ud_keys.py              # Type-safe keys
│   ├── option_types.py         # Enums
│   └── defaults.yaml           # Default values
├── services/                    # Module services
│   ├── events.py               # Event definitions
│   ├── events_data.py          # Event dataclasses
│   ├── local_event_bus.py      # Local event system
│   └── file_info_service.py    # File enrichment
└── pipeline/                    # Data processing
    ├── dw_director.py          # Processing orchestrator
    ├── dw_pipeline.py          # Core processing functions
    └── statement_handlers/     # Bank-specific parsers
```

## Presenter Architecture (Decomposed)

### 1. UpdateDataPresenter (Main Coordinator)
**Purpose**: Manager orchestration and lifecycle management
**Key Responsibilities**:
- Manager instantiation with dependency injection
- Signal routing to appropriate managers
- Module lifecycle (setup, cleanup, transitions)

**Critical Methods**:
```python
def _connect_signals(self):
    # Creates all managers with proper dependencies
    # Routes view signals to appropriate managers

def _refresh_content(self, **params):
    # Called when module becomes visible
    # Initializes UI state and configuration
```

### 2. StateManager (Consolidated State + UI Sync)
**Purpose**: Centralized state management and UI synchronization
**Key Responsibilities**:
- All presenter state (source, destination, processing status)
- State validation and updates
- State-to-view synchronization
- Widget state management

**State Properties**:
```python
@dataclass
class UpdateDataState:
    # Source configuration
    source_configured: bool = False
    source_type: str = ""  # "folder", "files", "auto_import"
    source_path: str = ""
    selected_files: List[str] = field(default_factory=list)

    # Destination configuration
    destination_configured: bool = False
    save_option: str = "csv"  # "csv", "master", "archive"
    save_path: str = ""
    update_database: bool = True

    # Processing state
    processing: bool = False
    can_process: bool = False
    process_button_text: str = "Select Files First"
    
    # UI state
    status_message: str = "Select source files or folder to begin"
    error_message: str = ""
    
    # Auto-import state
    auto_import_enabled: bool = False
    auto_import_pending_count: int = 0
```

**Critical Methods**:
```python
def sync_state_to_view(self):
    # MVP pattern: Presenter state → View display
    # Updates all UI elements based on current state

def update_guide_pane(self):
    # Contextual guidance based on current workflow state
    # Shows folder monitoring options when appropriate
```

### 3. FileManager (Consolidated File Operations)
**Purpose**: File/folder selection, save location, and folder monitoring
**Key Responsibilities**:
- File/folder selection dialogs
- File information enrichment via FileInfoService
- Save location selection and "same as source" logic
- Source and save option changes

**Key Methods**:
```python
def handle_source_select(self, selection_type):
    # Handles "folder" or "files" selection
    # Enriches file info and updates state

def handle_save_select(self):
    # Save location selection
    # "Same as source" logic integration
```

### 4. FileListManager (File List Operations)
**Purpose**: Canonical file list management
**Key Responsibilities**:
- Maintains canonical `file_paths_list`
- Handles file add/remove operations
- Folder monitoring integration
- Emits events for UI updates

**Key Methods**:
```python
def set_files(self, file_paths, source_path=""):
    # Set the canonical file list

def add_files(self, new_files):
    # Add files to the canonical list

def remove_file(self, file_path):
    # Remove a file from the canonical list

def set_folder_monitoring(self, folder_path, enabled):
    # Enable/disable folder monitoring
```

### 5. ProcessingManager (File Processing & Events)
**Purpose**: File processing execution and event handling  
**Key Responsibilities**:
- Process button handling and validation
- Job sheet creation and execution via dw_director
- Processing event handling (started, stats, completed)
- Error handling and user feedback

**Event Handlers**:
```python
def on_processing_started(self, job_sheet):
def on_processing_stats(self, stats):
def on_unrecognized_files(self, unrecognized_files):
def on_processing_completed(self, result):
```

## View Architecture

### Interface Abstraction (IUpdateDataView)
**Purpose**: Break circular dependencies between presenter and view  
**Pattern**: Protocol-based interface that presenter depends on

```python
class IUpdateDataView(Protocol):
    # Signals (View → Presenter)
    cancel_clicked = Signal()
    source_select_requested = Signal(str)  # selection_type: "folder" or "files"
    save_select_requested = Signal()
    source_option_changed = Signal(str)
    save_option_changed = Signal(str)
    process_clicked = Signal()
    update_database_changed = Signal(bool)
    
    # State queries (Presenter → View)
    def get_save_option(self) -> str: ...
    def get_update_database(self) -> bool: ...
    
    # State updates (Presenter → View)
    def set_save_select_enabled(self, enabled: bool) -> None: ...
    def set_source_option(self, option: str) -> None: ...
    def set_save_path(self, path: str) -> None: ...
    def set_process_button_text(self, text: str) -> None: ...
    def set_process_enabled(self, enabled: bool) -> None: ...
    
    # Dialogs (Presenter → View)
    def show_folder_dialog(self, title: str, initial_dir: str) -> str: ...
    def show_files_dialog(self, title: str, initial_dir: str) -> List[str]: ...
    def show_error(self, message: str) -> None: ...
    
    # Display management (Presenter → View)
    def display_selected_source(self, source_data: Dict[str, Any]) -> None: ...
    def display_enriched_file_info(self, file_info_list: List[Dict[str, Any]]) -> None: ...
    
    # Component lifecycle
    def cleanup(self) -> None: ...
    def show_component(self) -> None: ...
    def hide_component(self) -> None: ...
    
    # File list management
    def get_current_files(self) -> List[str]: ...
    def connect_file_list_manager(self, local_bus) -> None: ...
```

### Component-Based UI
- **CenterPanel**: File display, processing status
- **LeftPanel**: Source/destination options, action buttons

## Configuration System

### Type-Safe Configuration
```python
# ud_keys.py - Hierarchical key structure
class UpdateDataKeys:
    class Paths:
        LAST_SOURCE_DIR = "paths.last_source_dir"
        LAST_SAVE_DIR = "paths.last_save_dir"
    
    class Source:
        LAST_SOURCE_OPTION = "source.last_source_option"
```

### Module-Specific Wrapper
```python
# ud_config.py - Clean interface to core config
class UpdateDataConfig(BaseLocalConfig[UpdateDataKeys]):
    def get_defaults_file_path(self) -> Path:
        return Path(__file__).parent / "defaults.yaml"
```

## Event System

### Local Event Bus
**Purpose**: Decoupled communication within module  
**Usage**: Manager-to-manager communication, view updates

```python
# Event emission with dataclass
self.local_bus.emit(
    ViewEvents.FILE_LIST_UPDATED.value,
    FileListUpdatedEvent(files=self.file_paths_list, source_path=source_path)
)

# Event subscription  
self.local_bus.subscribe(ViewEvents.PROCESSING_STARTED.value, self.on_processing_started)
```

### Event Dataclasses
**Purpose**: Type-safe event data structures
**Usage**: Replaces dictionary-based event data

```python
@dataclass
class FileListUpdatedEvent:
    """Event data for file list updates."""
    files: List[str]
    source_path: str = ""
    timestamp: str = field(default_factory=_now_iso)
```

### Global Event Bus Integration
**Purpose**: Cross-module communication and core service integration  
**Usage**: Database updates, folder monitoring, module transitions

## Data Processing Pipeline

### Director Pattern (dw_director.py)
**Purpose**: Orchestrate file processing workflow  
**Process**:
1. File validation and handler selection
2. Data extraction and transformation
3. Backup creation and database updates
4. Event emission for progress tracking

### Statement Handlers
**Purpose**: Bank-specific file parsing  
**Pattern**: Registry-based handler selection by file characteristics

## Development Guidelines

### Adding New Features

1. **Identify Responsibility**: Which manager should handle the new feature?
2. **Update State**: Add necessary state properties to StateManager
3. **Implement Logic**: Add methods to appropriate manager
4. **Update UI**: Modify view interface and implementation
5. **Add Configuration**: Update keys, defaults, and config wrapper
6. **Test Integration**: Ensure manager coordination works

### Manager Communication

**Current Pattern**:
```python
# Event-driven communication
self.local_bus.emit(
    ViewEvents.FILE_LIST_UPDATED.value,
    FileListUpdatedEvent(files=self.file_paths_list, source_path=source_path)
)

# Event subscription
self.local_bus.subscribe(
    ViewEvents.FILE_LIST_UPDATED.value,
    self._on_file_list_updated
)
```

### Testing Strategy

1. **Unit Tests**: Each manager independently
2. **Integration Tests**: Manager coordination
3. **UI Tests**: View interface compliance
4. **Pipeline Tests**: File processing workflows

## Common Patterns

### Dependency Injection
```python
# In presenter _connect_signals()
self.file_manager = FileManager(
    self.view,
    self.state_manager,
    self.file_list_manager,
    self.local_bus,
    self.info_bar_service
)
```

### State Updates
```python
# Always update state first, then sync to view
self.state.source_configured = True
self.state.update_can_process()
self.sync_state_to_view()
```

### Error Handling
```python
# Use view interface for user feedback
if not validation_result:
    self.view.show_error("Validation failed: " + error_message)
    return
```

## Architecture Status

### Current Implementation
- **StateManager**: Consolidated state and UI synchronization
- **FileManager**: File operations and save location management
- **FileListManager**: Canonical file list and folder monitoring
- **ProcessingManager**: File processing pipeline integration

### Technical Debt
1. **Interface bypass**: Some direct calls to view components bypass interface abstraction
2. **Event system migration**: Some events could be replaced with direct interface method calls
3. **Dependency injection**: Services create their own repository instances instead of having them injected

## Quick Reference

### Key Files to Modify
- **New feature logic**: Appropriate manager in `_presenter/`
- **UI changes**: View components in `_view/`
- **Configuration**: `config/ud_keys.py` and `defaults.yaml`
- **Events**: `services/events_data.py`

### Common Tasks
- **Add new source type**: Extend FileManager and SourceOptions enum
- **Add processing step**: Modify ProcessingManager and pipeline
- **Add UI element**: Update view interface and implementation
- **Add configuration**: Update keys, defaults, and manager usage
- **Add file list feature**: Modify FileListManager

---
**Status**: Functional with identified technical debt  
**Next Phase**: Interface method vs event system refactoring
