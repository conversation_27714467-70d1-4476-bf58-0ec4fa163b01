# Winston Pragmatic Architecture Compliance Checklist

This checklist is for systematic review of the Update Data module’s UI orchestration and widget manager/controller architecture, as captured in `winston_architecture_insights.md`. Use it to ensure the codebase is fully aligned with the pragmatic, explicit, and maintainable architecture described.

---

## 1. **Naming Clarity**
- [ ] All UI orchestration classes use explicit, role-reflecting names (e.g., `FilePanelController`, `SourceSelectionController`, `ArchiveSelectionController`).
- [ ] No orchestration class uses the generic name `Manager` unless it is truly a pure state manager.
- [ ] Folder containing orchestration classes is named `controllers` or `ui_controllers`, not `managers`.
- [ ] File and class names are consistent and discoverable throughout the module.

## 2. **Controller Placement**
- [ ] Widget-specific, thin, stateless controllers are located with their widget (e.g., in `components/`).
- [ ] Controllers that coordinate multiple widgets or contain stateful logic are located in `controllers/` or `ui_controllers/`.
- [ ] No orchestration logic is left in the view or widget files unless it is truly widget-internal.

## 3. **Interdependent Logic**
- [ ] Interdependent UI groups (e.g., source/archive selection) are managed by a single controller if their logic is coupled (e.g., “same as source” option).
- [ ] Controllers are only split if logic is truly independent and no cross-group coordination is required.
- [ ] No unnecessary UI events/signals are used for tightly coupled widget groups; direct controller logic is used instead.

## 4. **Separation of Concerns**
- [ ] Controllers do not contain business logic (which belongs in `services/` or equivalent).
- [ ] Controllers do not contain direct UI rendering code (which belongs in views/widgets).
- [ ] Controllers expose a clear, minimal public API for orchestration and coordination.

## 5. **Architecture Documentation**
- [ ] `winston_architecture_insights.md` is present and up-to-date.
- [ ] The rationale for controller placement and naming is documented and matches the codebase.
- [ ] Any deviations from the recommended pattern are documented with justification.

## 6. **Maintainability & Discoverability**
- [ ] New contributors can easily locate orchestration logic for any UI feature.
- [ ] Folder and file structure matches the documented architecture.
- [ ] Naming and placement conventions are enforced in code review.

## 7. **Legacy Code Removal**
- [ ] No legacy `manager` classes remain unless justified by pure state management role.
- [ ] No duplicated orchestration logic exists across widgets and controllers.

---

**Instructions:**
- For each item, check for compliance in the codebase.
- Provide evidence (file/class names, code snippets, or comments) for each item during review.
- Document any exceptions or deviations in the architecture insights file.
