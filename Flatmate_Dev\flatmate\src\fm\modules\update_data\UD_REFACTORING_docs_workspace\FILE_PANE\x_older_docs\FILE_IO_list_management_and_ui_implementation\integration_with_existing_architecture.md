# Integration with Existing Architecture

## Overview

This document shows how to integrate the new FileListManager with the existing Update Data module architecture, addressing the recent add/remove issues while maintaining compatibility.

## Current Architecture Integration Points

### 1. Presenter Integration (ud_presenter.py)

```python
# ud_presenter.py - Add FileListManager to dependency injection

class UpdateDataPresenter:
    def __init__(self, view: IUpdateDataView):
        self.view = view
        # ... existing initialization ...
        
        # Create FileListManager before other managers
        self.file_list_manager = FileListManager(
            folder_monitor_service=self.folder_monitor_service,
            local_bus=self.local_bus,
            file_info_service=FileInfoService()
        )
        
        # Pass FileListManager to other managers
        self._connect_signals()
    
    def _connect_signals(self):
        """Enhanced signal connection with FileListManager."""
        # ... existing managers ...
        
        # Update FileManager initialization to use FileListManager
        self.file_manager = FileManager(
            self.view,
            self.state_manager,
            self.file_list_manager,  # Add this dependency
            self.local_bus,
            self.info_bar_service
        )
        
        # Connect file pane signals to FileListManager
        if hasattr(self.view, 'center_panel') and hasattr(self.view.center_panel, 'file_pane'):
            file_pane = self.view.center_panel.file_pane
            
            # Connect add/remove signals
            file_pane.publish_file_removed.connect(
                lambda file_path: self.file_list_manager.remove_file(file_path)
            )
            
            file_pane.publish_files_added.connect(
                lambda files: self.file_list_manager.add_files(files)
            )
            
            # Connect folder monitoring toggle
            file_pane.publish_toggle_folder_monitoring_requested.connect(
                lambda folder_path, enabled: self.file_list_manager.set_folder_monitoring(folder_path, enabled)
            )
```

### 2. FileManager Refactoring

```python
# _presenter/file_manager.py - Updated to delegate to FileListManager

class FileManager:
    """
    Handles file/folder selection dialogs and save location logic.
    Delegates file list management to FileListManager.
    """
    
    def __init__(self, view, state_manager, file_list_manager, local_bus, info_bar_service):
        self.view = view
        self.state_manager = state_manager
        self.file_list_manager = file_list_manager  # NEW: Delegate file list operations
        self.local_bus = local_bus
        self.info_bar_service = info_bar_service
        
        # File info service for enrichment
        self.file_info_service = FileInfoService()
        
        # Track selected source for "same as source" functionality
        self.selected_source = None
        
        # REMOVED: self.file_paths_list - now owned by FileListManager
    
    def _select_files(self):
        """Select individual files - delegate list management to FileListManager."""
        try:
            # ... existing dialog logic ...
            
            file_paths = self.view.show_files_dialog("Select CSV Files to Process", last_dir)
            if file_paths:
                # Save directory for next time
                first_file_dir = os.path.dirname(file_paths[0])
                ud_config.set_value(UpdateDataKeys.Paths.LAST_SOURCE_DIR, first_file_dir)
                
                # CHANGED: Delegate to FileListManager instead of managing list directly
                self.file_list_manager.set_files(file_paths)
                
                # Update state
                self.state.selected_files = file_paths
                self.state.source_type = 'files'
                self.selected_source = file_paths
                
                # Enrich file info and display
                enriched_info = self.enrich_file_info(file_paths)
                self.view.display_enriched_file_info(enriched_info)
                
                # Update can_process flag
                self.state.update_can_process()
                
                # REMOVED: Direct call to view.center_panel.set_files()
                # This is now handled by FileListManager events
                
                log.debug(f"Selected {len(file_paths)} files")
                
        except Exception as e:
            log.error(f"Error selecting files: {e}")
    
    def _select_folder(self):
        """Select folder - delegate list management to FileListManager."""
        try:
            # ... existing dialog logic ...
            
            folder_path = self.view.show_folder_dialog("Select Folder Containing CSV Files", last_dir)
            if folder_path:
                # Save directory and discover files
                ud_config.set_value(UpdateDataKeys.Paths.LAST_SOURCE_DIR, folder_path)
                discovered_files = self._discover_files_in_folder(folder_path)
                
                # CHANGED: Delegate to FileListManager
                self.file_list_manager.set_files(discovered_files, folder_path)
                
                # Update state
                self.selected_source = folder_path
                self.state.selected_folder = folder_path
                self.state.source_type = 'folder'
                
                enriched_files = self.enrich_file_info(discovered_files)
                self.state.selected_files = enriched_files
                self.state.update_can_process()
                
                # REMOVED: Direct calls to view.center_panel.set_files()
                # REMOVED: Manual event emissions - handled by FileListManager
                
                log.debug(f"Selected folder with {len(discovered_files)} files")
                
        except Exception as e:
            log.error(f"Error selecting folder: {e}")
    
    # REMOVED: handle_folder_monitor_file_discovered - now handled by FileListManager
    # REMOVED: toggle_folder_monitoring - now handled by FileListManager
    # REMOVED: handle_monitor_folder_change - now handled by FileListManager
    
    # Keep existing save location methods unchanged
    def handle_save_select(self):
        """Handle save location selection - unchanged."""
        # ... existing implementation ...
    
    def handle_save_option_change(self, option):
        """Handle save option changes - unchanged."""
        # ... existing implementation ...
```

### 3. FilePane Event Subscription

```python
# _view/center_panel_components/file_pane.py - Subscribe to FileListManager events

class FilePane(BasePane):
    def __init__(self, parent=None):
        super().__init__(parent)
        self._source_folder_path = None
        self._init_ui()
        self._connect_signals()
        # NEW: Subscribe to file list events
        self._subscribe_to_file_list_events()
    
    def _subscribe_to_file_list_events(self):
        """Subscribe to FileListManager events via presenter."""
        # This will be connected by the presenter when it has access to local_bus
        pass
    
    def connect_to_file_list_manager(self, local_bus):
        """Connect to FileListManager events - called by presenter."""
        try:
            local_bus.subscribe(ViewEvents.FILE_LIST_UPDATED.value, self._on_file_list_updated)
            local_bus.subscribe(ViewEvents.FILE_ADDED.value, self._on_file_added)
            local_bus.subscribe(ViewEvents.FILE_REMOVED.value, self._on_file_removed)
            log.debug("[FILE_PANE] Connected to FileListManager events")
        except Exception as e:
            log.error(f"[FILE_PANE] Error connecting to file list events: {e}")
    
    def _on_file_list_updated(self, event_data):
        """Handle file list updates from FileListManager."""
        try:
            if hasattr(event_data, 'files') and hasattr(event_data, 'source_path'):
                self.set_files(event_data.files, event_data.source_path)
                log.debug(f"[FILE_PANE] Updated display with {len(event_data.files)} files")
        except Exception as e:
            log.error(f"[FILE_PANE] Error handling file list update: {e}")
    
    def _on_file_added(self, event_data):
        """Handle individual file addition."""
        try:
            if hasattr(event_data, 'file_path'):
                # Could add visual feedback for file addition
                log.debug(f"[FILE_PANE] File added: {os.path.basename(event_data.file_path)}")
        except Exception as e:
            log.error(f"[FILE_PANE] Error handling file added: {e}")
    
    def _on_file_removed(self, event_data):
        """Handle individual file removal."""
        try:
            if hasattr(event_data, 'file_path'):
                # Could add visual feedback for file removal
                log.debug(f"[FILE_PANE] File removed: {os.path.basename(event_data.file_path)}")
        except Exception as e:
            log.error(f"[FILE_PANE] Error handling file removed: {e}")
    
    # Existing methods remain unchanged
    def set_files(self, files: list, source_dir: str = ""):
        """Set files to display - unchanged implementation."""
        # ... existing implementation ...
    
    def _on_monitor_folder_toggled(self, enabled: bool):
        """Handle folder monitoring toggle - now emits event for FileListManager."""
        if self._source_folder_path:
            # Emit signal that will be connected to FileListManager
            self.publish_toggle_folder_monitoring_requested.emit(self._source_folder_path, enabled)
```

### 4. View Interface Updates

```python
# interface/i_view_interface.py - Add method for FileListManager connection

class IUpdateDataView(Protocol):
    # ... existing methods ...
    
    def connect_file_list_manager(self, local_bus) -> None:
        """Connect file pane to FileListManager events."""
        ...
```

```python
# ud_view.py - Implement the connection method

class UpdateDataView(IUpdateDataView):
    # ... existing implementation ...
    
    def connect_file_list_manager(self, local_bus):
        """Connect file pane to FileListManager events."""
        try:
            if hasattr(self.center_panel, 'file_pane'):
                self.center_panel.file_pane.connect_to_file_list_manager(local_bus)
        except Exception as e:
            log.error(f"Error connecting file list manager: {e}")
```

### 5. Complete Presenter Integration

```python
# ud_presenter.py - Complete integration

class UpdateDataPresenter:
    def _connect_signals(self):
        """Enhanced signal connection with FileListManager integration."""
        # Create all managers with FileListManager dependency
        self.file_list_manager = FileListManager(
            folder_monitor_service=self.folder_monitor_service,
            local_bus=self.local_bus,
            file_info_service=FileInfoService()
        )
        
        # Create other managers with FileListManager dependency
        self.file_manager = FileManager(
            self.view,
            self.state_manager,
            self.file_list_manager,  # Pass FileListManager
            self.local_bus,
            self.info_bar_service
        )
        
        # ... other managers ...
        
        # Connect view to FileListManager events
        self.view.connect_file_list_manager(self.local_bus)
        
        # Connect view signals to managers
        self.view.source_select_requested.connect(self.file_manager.handle_source_select)
        # ... other signal connections ...
```

## Migration Benefits

### 1. Addresses Recent Issues
- **Centralized add/remove logic** fixes recent add/remove problems
- **Event-driven updates** ensure UI consistency
- **Error handling** prevents silent failures

### 2. Maintains Compatibility
- **Existing interfaces unchanged** - no breaking changes
- **Same signal patterns** - familiar to existing code
- **Gradual migration** - can be implemented incrementally

### 3. Improved Architecture
- **Clear separation of concerns** - FileManager focuses on dialogs
- **Single source of truth** - FileListManager owns canonical list
- **Event-driven communication** - consistent pattern throughout

### 4. Enhanced Monitoring
- **Simple dataclass** for folder status as requested
- **Integration with existing FolderMonitorService**
- **Persistent monitoring configuration**

This integration approach maintains the existing MVP architecture while providing the focused file list management functionality requested by the user.
