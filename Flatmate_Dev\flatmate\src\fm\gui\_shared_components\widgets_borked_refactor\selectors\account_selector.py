"""
Account Selector Component

A compact multi-select widget for choosing accounts that handles long account numbers
gracefully and follows the same pattern as column selection.
"""

from typing import List, Set, Optional
from PySide6.QtCore import Signal, Qt
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QDialog, QDialogButtonBox, QCheckBox, QScrollArea, QFrame
)


class AccountSelectionDialog(QDialog):
    """Dialog for selecting multiple accounts with checkboxes."""
    
    def __init__(self, available_accounts: List[str], selected_accounts: Set[str], parent=None):
        """Initialize the account selection dialog.
        
        Args:
            available_accounts: List of available account numbers/names
            selected_accounts: Set of currently selected accounts
            parent: Parent widget
        """
        super().__init__(parent)
        self.setWindowTitle("Select Accounts")
        self.setModal(True)
        self.setMinimumSize(400, 300)
        
        self.available_accounts = available_accounts
        self.selected_accounts = selected_accounts.copy()
        self.checkboxes = {}
        
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the dialog UI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(12)
        
        # Header
        header = QLabel("Select accounts to include in filter:")
        header.setStyleSheet("font-weight: bold; color: #CCCCCC;")
        layout.addWidget(header)
        
        # Quick selection buttons
        quick_layout = QHBoxLayout()
        
        self.select_all_btn = QPushButton("Select All")
        self.select_all_btn.setMaximumWidth(100)
        self.select_none_btn = QPushButton("Select None")
        self.select_none_btn.setMaximumWidth(100)
        
        quick_layout.addWidget(self.select_all_btn)
        quick_layout.addWidget(self.select_none_btn)
        quick_layout.addStretch()
        
        layout.addLayout(quick_layout)
        
        # Scrollable account list
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 1px solid #333333;
                border-radius: 4px;
                background-color: #1E1E1E;
            }
        """)
        
        # Container for checkboxes
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setContentsMargins(8, 8, 8, 8)
        scroll_layout.setSpacing(4)
        
        # Add "All Accounts" option
        all_checkbox = QCheckBox("All Accounts")
        all_checkbox.setObjectName("all_accounts")
        all_checkbox.setChecked(len(self.selected_accounts) == 0)
        all_checkbox.setStyleSheet("""
            QCheckBox {
                color: #FFFFFF;
                font-weight: bold;
                padding: 4px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
        """)
        self.checkboxes["all_accounts"] = all_checkbox
        scroll_layout.addWidget(all_checkbox)
        
        # Add separator
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setStyleSheet("color: #333333;")
        scroll_layout.addWidget(separator)
        
        # Add individual accounts
        for account in self.available_accounts:
            checkbox = QCheckBox(self._format_account_display(account))
            checkbox.setObjectName(account)
            checkbox.setChecked(account in self.selected_accounts)
            checkbox.setStyleSheet("""
                QCheckBox {
                    color: #FFFFFF;
                    padding: 2px;
                    font-family: 'Courier New', monospace;
                    font-size: 11px;
                }
                QCheckBox::indicator {
                    width: 14px;
                    height: 14px;
                }
            """)
            self.checkboxes[account] = checkbox
            scroll_layout.addWidget(checkbox)
        
        scroll_layout.addStretch()
        scroll_area.setWidget(scroll_widget)
        layout.addWidget(scroll_area)
        
        # Dialog buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        layout.addWidget(button_box)
        
        # Store references
        self.button_box = button_box
    
    def _format_account_display(self, account: str) -> str:
        """Format account number for display (truncate if too long)."""
        if len(account) > 25:
            return f"{account[:22]}..."
        return account
    
    def _connect_signals(self):
        """Connect dialog signals."""
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        
        # Quick selection buttons
        self.select_all_btn.clicked.connect(self._select_all)
        self.select_none_btn.clicked.connect(self._select_none)
        
        # All accounts checkbox
        self.checkboxes["all_accounts"].toggled.connect(self._on_all_accounts_toggled)
        
        # Individual account checkboxes
        for account in self.available_accounts:
            self.checkboxes[account].toggled.connect(self._on_account_toggled)
    
    def _select_all(self):
        """Select all individual accounts."""
        for account in self.available_accounts:
            self.checkboxes[account].setChecked(True)
        self.checkboxes["all_accounts"].setChecked(False)
    
    def _select_none(self):
        """Deselect all accounts (select "All Accounts")."""
        for account in self.available_accounts:
            self.checkboxes[account].setChecked(False)
        self.checkboxes["all_accounts"].setChecked(True)
    
    def _on_all_accounts_toggled(self, checked):
        """Handle All Accounts checkbox toggle."""
        if checked:
            # Uncheck all individual accounts
            for account in self.available_accounts:
                self.checkboxes[account].setChecked(False)
    
    def _on_account_toggled(self, checked):
        """Handle individual account checkbox toggle."""
        if checked:
            # Uncheck "All Accounts"
            self.checkboxes["all_accounts"].setChecked(False)
        else:
            # If no accounts selected, check "All Accounts"
            any_selected = any(self.checkboxes[acc].isChecked() for acc in self.available_accounts)
            if not any_selected:
                self.checkboxes["all_accounts"].setChecked(True)
    
    def get_selected_accounts(self) -> Set[str]:
        """Get the set of selected accounts."""
        if self.checkboxes["all_accounts"].isChecked():
            return set()  # Empty set means "all accounts"
        
        return {
            account for account in self.available_accounts
            if self.checkboxes[account].isChecked()
        }


class AccountSelector(QWidget):
    """
    Compact account selector that shows selection summary and opens dialog for details.
    
    Signals:
        selection_changed: Emitted when account selection changes
    """
    
    selection_changed = Signal(set)  # Emits set of selected accounts
    
    def __init__(self, parent=None):
        """Initialize the account selector."""
        super().__init__(parent)
        self.setObjectName("AccountSelector")
        
        self.available_accounts = []
        self.selected_accounts = set()
        
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components with height optimization."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(3)  # Reduced from 4 for height optimization

        # Label above button
        self.label = QLabel("Accounts")
        self.label.setObjectName("account_label")
        layout.addWidget(self.label)

        # Selection button with optimized height
        self.selection_btn = QPushButton("All Accounts")
        self.selection_btn.setObjectName("account_selection_btn")
        self.selection_btn.setMinimumHeight(26)  # Reduced from 28 for height optimization
        self.selection_btn.setMaximumHeight(26)  # Add max height to prevent expansion
        layout.addWidget(self.selection_btn)
        
        # Apply styling
        self.setStyleSheet("""
            QLabel#account_label {
                color: #CCCCCC;
                font-size: 12px;
                font-weight: bold;
            }
            
            QPushButton#account_selection_btn {
                background-color: #1E1E1E;
                color: #FFFFFF;
                border: 1px solid #333333;
                border-radius: 3px;
                padding: 4px 8px;
                font-size: 12px;
                text-align: left;
            }
            
            QPushButton#account_selection_btn:hover {
                border-color: #3B8A45;
            }
            
            QPushButton#account_selection_btn:pressed {
                background-color: #2A2A2A;
            }
        """)
    
    def _connect_signals(self):
        """Connect widget signals."""
        self.selection_btn.clicked.connect(self._show_selection_dialog)
    
    def set_available_accounts(self, accounts: List[str]):
        """Set the list of available accounts."""
        self.available_accounts = accounts
        self.selected_accounts = set()  # Reset selection
        self._update_button_text()
    
    def set_selected_accounts(self, accounts: Set[str]):
        """Set the selected accounts."""
        self.selected_accounts = accounts.copy()
        self._update_button_text()
    
    def get_selected_accounts(self) -> Set[str]:
        """Get the currently selected accounts."""
        return self.selected_accounts.copy()
    
    def _update_button_text(self):
        """Update the button text based on current selection."""
        if not self.selected_accounts:
            self.selection_btn.setText("All Accounts")
        elif len(self.selected_accounts) == 1:
            account = next(iter(self.selected_accounts))
            display_text = self._format_account_for_button(account)
            self.selection_btn.setText(display_text)
        else:
            self.selection_btn.setText(f"{len(self.selected_accounts)} accounts selected")
    
    def _format_account_for_button(self, account: str) -> str:
        """Format account for button display (truncate if needed)."""
        if len(account) > 20:
            return f"{account[:17]}..."
        return account
    
    def _show_selection_dialog(self):
        """Show the account selection dialog."""
        if not self.available_accounts:
            return
        
        dialog = AccountSelectionDialog(
            self.available_accounts, 
            self.selected_accounts, 
            self
        )
        
        if dialog.exec() == QDialog.DialogCode.Accepted:
            new_selection = dialog.get_selected_accounts()
            if new_selection != self.selected_accounts:
                self.selected_accounts = new_selection
                self._update_button_text()
                self.selection_changed.emit(self.selected_accounts)
