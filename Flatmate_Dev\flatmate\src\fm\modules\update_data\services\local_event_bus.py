#!/usr/bin/env python3
"""
Local Event Bus for Update Data Module

Provides internal event coordination separate from global event bus.
Handles UI state management, view coordination, and local business events.
"""

from typing import Any, Callable, Dict, List, Optional
import threading
from datetime import datetime
from enum import Enum

# Import event dataclasses
from .._ui.ui_events import (
    SourceSelectRequestEvent,
    SourceDiscoveredEvent,
    DestinationConfiguredEvent,
    UiStateUpdateEvent,
    BusinessErrorEvent,
    DialogRequestEvent,
    FilesDialogCompletedEvent,
    FolderDialogCompletedEvent,
    FileListUpdatedEvent,
    FileAddedEvent,
    FileRemovedEvent,
    FolderMonitoringToggledEvent,
    FilesDiscoveredEvent
)


class LocalEventBus:
    """
    Local event bus for internal module communication.
    
    Separate from global event bus to avoid cross-module pollution
    and provide module-specific event handling with debugging capabilities.
    """
    
    def __init__(self, module_name: str):
        self.module_name = module_name
        self._listeners: Dict[str, List[Callable]] = {}
        self._lock = threading.Lock()
        self._event_log: List[Dict] = []
        self._max_log_size = 1000  # Prevent memory bloat
        
    def subscribe(self, event_type: str, listener: Callable):
        """
        Subscribe to local events.
        
        Args:
            event_type: String identifier for the event
            listener: Callable that will handle the event
        """
        with self._lock:
            if event_type not in self._listeners:
                self._listeners[event_type] = []
            self._listeners[event_type].append(listener)
            
    def unsubscribe(self, event_type: str, listener: Callable):
        """
        Unsubscribe from local events.
        
        Args:
            event_type: String identifier for the event
            listener: Callable to remove
        """
        with self._lock:
            if event_type in self._listeners:
                try:
                    self._listeners[event_type].remove(listener)
                except ValueError:
                    pass  # Listener not found
                    
    def emit(self, event_type: str, data: Any = None):
        """
        Emit local event to all subscribers.
        
        Args:
            event_type: String identifier for the event
            data: Optional data to pass to listeners
        """
        # Log event for debugging
        self._log_event(event_type, data)
        
        # Get listeners (copy to avoid modification during iteration)
        listeners = self._listeners.get(event_type, []).copy()
        
        # Notify all listeners
        for listener in listeners:
            try:
                listener(data)
            except Exception as e:
                print(f"Local event error in {self.module_name}.{event_type}: {e}")
                
    def bridge_to_global(self, local_event: str, global_event: str, transform_fn: Optional[Callable] = None):
        """
        Bridge local events to global event bus.
        
        Args:
            local_event: Local event type to bridge
            global_event: Global event type to emit
            transform_fn: Optional function to transform data before bridging
        """
        def bridge_handler(data):
            transformed_data = transform_fn(data) if transform_fn else data
            # Import here to avoid circular imports
            from fm.core.services.event_bus import global_event_bus
            global_event_bus.publish(global_event, transformed_data)
            
        self.subscribe(local_event, bridge_handler)
        
    def get_event_log(self, event_type: Optional[str] = None, limit: int = 100) -> List[Dict]:
        """
        Get recent event log for debugging.
        
        Args:
            event_type: Optional filter by event type
            limit: Maximum number of events to return
            
        Returns:
            List of event log entries
        """
        with self._lock:
            events = self._event_log[-limit:] if limit else self._event_log
            
            if event_type:
                events = [e for e in events if e['type'] == event_type]
                
            return events
            
    def clear_event_log(self):
        """Clear the event log."""
        with self._lock:
            self._event_log.clear()
            
    def _log_event(self, event_type: str, data: Any):
        """Log event for debugging purposes."""
        with self._lock:
            self._event_log.append({
                'type': event_type,
                'data': data,
                'timestamp': datetime.now(),
                'module': self.module_name
            })
            
            # Trim log if it gets too large
            if len(self._event_log) > self._max_log_size:
                self._event_log = self._event_log[-self._max_log_size//2:]


class ViewEvents(Enum):
    """Local view events for Update Data module."""
    
    # User Actions (View → Presenter)
    SOURCE_SELECT_REQUESTED = "source_select_requested"
    DESTINATION_SELECT_REQUESTED = "destination_select_requested"
    PROCESS_REQUESTED = "process_requested"
    CANCEL_REQUESTED = "cancel_requested"
    SOURCE_OPTION_CHANGED = "source_option_changed"
    SAVE_OPTION_CHANGED = "save_option_changed"
    UPDATE_DATABASE_CHANGED = "update_database_changed"
    
    # Business Events (Presenter → State)
    SOURCE_DISCOVERED = "source_discovered"
    DESTINATION_CONFIGURED = "destination_configured"
    PROCESSING_STARTED = "processing_started"
    PROCESSING_COMPLETED = "processing_completed"
    BUSINESS_ERROR = "business_error"
    
    # State Events (State → View)
    UI_STATE_CHANGED = "ui_state_changed"
    STATUS_MESSAGE_CHANGED = "status_message_changed"
    
    # Dialog Events (Bidirectional)
    FOLDER_DIALOG_REQUESTED = "folder_dialog_requested"
    FOLDER_DIALOG_COMPLETED = "folder_dialog_completed"
    FILES_DIALOG_REQUESTED = "files_dialog_requested"
    FILES_DIALOG_COMPLETED = "files_dialog_completed"
    ERROR_DIALOG_REQUESTED = "error_dialog_requested"
    SUCCESS_DIALOG_REQUESTED = "success_dialog_requested"

    #File_Display Events
    FILE_DISPLAY_UPDATED = "file_display_updated"
     #>>NEW SPECULATIVE:
    FILE_PATHS_POPULATED  = "all_files_removed"
    FILE_PATHS_CLEARED = "filepaths_updated"

    # File list management events (FileListManager)
    FILE_LIST_UPDATED = "file_list_updated"
    FILE_ADDED = "file_added"
    FILE_REMOVED = "file_removed"
    FOLDER_MONITORING_TOGGLED = "folder_monitoring_toggled"
    FILES_DISCOVERED = "files_discovered"

    # other filepath related events
    #>> # TODO '''I'd like to see each a class built for each widget group definig what events they listen to and how it treats them - probably not here?'''
# EventDataFactory has been replaced with proper dataclasses in events_data.py


# Module-specific event bus instance
update_data_local_bus = LocalEventBus('update_data')


# Convenience functions for common event patterns
def emit_user_action(action_type: ViewEvents, data: Dict = None):
    """Emit a user action event."""
    update_data_local_bus.emit(action_type.value, data)


def emit_business_event(event_type: ViewEvents, data: Dict = None):
    """Emit a business logic event."""
    update_data_local_bus.emit(event_type.value, data)


def emit_ui_update(ui_state: Dict):
    """Emit a UI state update event."""
    update_data_local_bus.emit(ViewEvents.UI_STATE_CHANGED.value, ui_state)


def subscribe_to_view_events(event_type: ViewEvents, handler: Callable):
    """Subscribe to view events with type safety."""
    update_data_local_bus.subscribe(event_type.value, handler)


def get_debug_log(event_filter: str = None, limit: int = 50) -> List[Dict]:
    """Get debug event log."""
    return update_data_local_bus.get_event_log(event_filter, limit)


def setup_global_event_bridges():
    """
    Set up bridges between local events and global events.

    This connects important local events to the existing global event system
    so other modules can be notified of significant Update Data events.
    """
    # Bridge processing events to global events
    update_data_local_bus.bridge_to_global(
        ViewEvents.PROCESSING_STARTED.value,
        'FILE_PROCESSING_STARTED',  # Global event name
        transform_fn=lambda data: data.get('job_context', {})
    )

    update_data_local_bus.bridge_to_global(
        ViewEvents.PROCESSING_COMPLETED.value,
        'FILE_PROCESSING_COMPLETED',  # Global event name
        transform_fn=lambda data: {
            'status': 'success' if data.get('success', False) else 'error',
            'processed_count': data.get('result', {}).get('processed_count', 0),
            'message': data.get('result', {}).get('message', ''),
            'error': data.get('result', {}).get('error', None)
        }
    )

    # Bridge source discovery to global events (for navigation logging)
    update_data_local_bus.bridge_to_global(
        ViewEvents.SOURCE_DISCOVERED.value,
        'UPDATE_DATA_SOURCE_SELECTED',  # Global event name
        transform_fn=lambda data: {
            'module': 'update_data',
            'source_type': data.get('source_type'),
            'file_count': data.get('file_count', 0),
            'timestamp': data.get('timestamp')
        }
    )

    print("[EVENT_BRIDGE] Global event bridges set up successfully")
