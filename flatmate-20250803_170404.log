2025-08-03 17:04:04 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-08-03 17:04:09 - [fm.core.services.folder_monitor_service] [INFO] - FolderMonitorService initialized
2025-08-03 17:04:09 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-03 17:04:09 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-08-03 17:04:09 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-08-03 17:04:10 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded 0 component defaults for categorize from C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\config\defaults.yaml
2025-08-03 17:04:10 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 0 user preferences
2025-08-03 17:04:10 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-08-03 17:04:10 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-08-03 17:04:10 - [fm.main] [INFO] - Application starting...
2025-08-03 17:04:17 - [fm.main] [INFO] - 
=== Initializing Database & Cache ===
2025-08-03 17:04:17 - [fm.core.data_services.db_io_service] [INFO] - Initializing DBIOService singleton...
2025-08-03 17:04:17 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-08-03 17:04:17 - [fm.core.database.sql_repository.cached_sqlite_repository] [DEBUG] - CachedSQLiteRepository initialized
2025-08-03 17:04:17 - [fm.core.database.sql_repository.cached_sqlite_repository] [INFO] - Warming transaction cache...
2025-08-03 17:04:18 - [fm.core.database.sql_repository.cached_sqlite_repository] [INFO] - Cache warmed successfully: 2166 transactions, 3 unique accounts in 1.14s
2025-08-03 17:04:18 - [fm.core.data_services.db_io_service] [INFO] - DBIOService initialized with cache: 2166 transactions
2025-08-03 17:04:18 - [fm.main] [INFO] - 
=== Initializing Auto-Import Manager ===
2025-08-03 17:04:18 - [fm.core.services.folder_monitor_service] [INFO] - Folder monitor worker thread started
2025-08-03 17:04:18 - [fm.core.services.folder_monitor_service] [INFO] - Folder monitor service started
2025-08-03 17:04:18 - [fm.main] [INFO] - 
=== Setting up Module Coordinator ===
2025-08-03 17:04:18 - [fm.module_coordinator] [INFO] - Initializing Module Coordinator
2025-08-03 17:04:18 - [fm.module_coordinator] [DEBUG] - Loaded recent modules: ['home', 'categorize', 'update_data']
2025-08-03 17:04:18 - [fm.module_coordinator] [INFO] - Creating all modules (eager loading)
2025-08-03 17:04:18 - [fm.modules.base.base_presenter] [DEBUG] - Initialized HomePresenter
2025-08-03 17:04:18 - [fm.modules.home.home_presenter] [DEBUG] - Home Presenter initialization complete
2025-08-03 17:04:18 - [fm.modules.base.base_presenter] [DEBUG] - Initialized UpdateDataPresenter
2025-08-03 17:04:18 - [fm.modules.update_data.ud_presenter] [DEBUG] - log level set to debug in ud_presenter.py
2025-08-03 17:04:18 - [fm.modules.update_data.ud_presenter] [DEBUG] - UPDATE_DATA: Debug logging enabled for console output
2025-08-03 17:04:18 - [fm.modules.base.base_presenter] [DEBUG] - Initialized CategorizePresenter
2025-08-03 17:04:18 - [fm.module_coordinator] [INFO] - Setting up home module
2025-08-03 17:04:18 - [fm.modules.base.base_presenter] [INFO] - Setting up HomePresenter
2025-08-03 17:04:19 - [fm.modules.home.home_presenter] [DEBUG] - Connecting Home View signals
2025-08-03 17:04:19 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter setup complete
2025-08-03 17:04:19 - [fm.module_coordinator] [INFO] - Setting up update_data module
2025-08-03 17:04:19 - [fm.modules.base.base_presenter] [INFO] - Setting up UpdateDataPresenter
2025-08-03 17:04:19 - [fm.main] [CRITICAL] - Fatal error during application initialization: name 'FilePane' is not defined
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\main.py", line 90, in initialize_application
    coordinator.initialize_modules()  # Heavy table rendering happens here (4.46s)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\module_coordinator.py", line 64, in initialize_modules
    module.setup()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\base\base_presenter.py", line 85, in setup
    self.view = self._create_view()
                ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\ud_presenter.py", line 109, in _create_view
    return UpdateDataView(self.main_window, gui_config=self.gui_config, gui_keys=self.gui_keys)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_ui\ud_view.py", line 62, in __init__
    super().__init__(parent, gui_config, gui_keys)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\base\base_module_view.py", line 65, in __init__
    self.setup_ui()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_ui\ud_view.py", line 68, in setup_ui
    self.center_display = CenterPanelManager()
                          ~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_ui\_view\center_panel_layout.py", line 39, in __init__
    self._init_ui()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_ui\_view\center_panel_layout.py", line 52, in _init_ui
    self._create_panes()
    ~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_ui\_view\center_panel_layout.py", line 64, in _create_panes
    self.file_pane = FilePane()
                     ^^^^^^^^
NameError: name 'FilePane' is not defined
2025-08-03 17:04:19 - [fm.main] [CRITICAL] - Exception details: Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\main.py", line 90, in initialize_application
    coordinator.initialize_modules()  # Heavy table rendering happens here (4.46s)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\module_coordinator.py", line 64, in initialize_modules
    module.setup()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\base\base_presenter.py", line 85, in setup
    self.view = self._create_view()
                ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\ud_presenter.py", line 109, in _create_view
    return UpdateDataView(self.main_window, gui_config=self.gui_config, gui_keys=self.gui_keys)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_ui\ud_view.py", line 62, in __init__
    super().__init__(parent, gui_config, gui_keys)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\base\base_module_view.py", line 65, in __init__
    self.setup_ui()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_ui\ud_view.py", line 68, in setup_ui
    self.center_display = CenterPanelManager()
                          ~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_ui\_view\center_panel_layout.py", line 39, in __init__
    self._init_ui()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_ui\_view\center_panel_layout.py", line 52, in _init_ui
    self._create_panes()
    ~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_ui\_view\center_panel_layout.py", line 64, in _create_panes
    self.file_pane = FilePane()
                     ^^^^^^^^
NameError: name 'FilePane' is not defined
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\main.py", line 90, in initialize_application
    coordinator.initialize_modules()  # Heavy table rendering happens here (4.46s)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\module_coordinator.py", line 64, in initialize_modules
    module.setup()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\base\base_presenter.py", line 85, in setup
    self.view = self._create_view()
                ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\ud_presenter.py", line 109, in _create_view
    return UpdateDataView(self.main_window, gui_config=self.gui_config, gui_keys=self.gui_keys)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_ui\ud_view.py", line 62, in __init__
    super().__init__(parent, gui_config, gui_keys)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\base\base_module_view.py", line 65, in __init__
    self.setup_ui()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_ui\ud_view.py", line 68, in setup_ui
    self.center_display = CenterPanelManager()
                          ~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_ui\_view\center_panel_layout.py", line 39, in __init__
    self._init_ui()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_ui\_view\center_panel_layout.py", line 52, in _init_ui
    self._create_panes()
    ~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_ui\_view\center_panel_layout.py", line 64, in _create_panes
    self.file_pane = FilePane()
                     ^^^^^^^^
NameError: name 'FilePane' is not defined
