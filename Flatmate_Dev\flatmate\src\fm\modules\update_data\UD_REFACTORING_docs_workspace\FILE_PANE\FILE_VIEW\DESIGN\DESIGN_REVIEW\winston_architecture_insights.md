# Update Data Module – Widget Manager & UI Orchestration Insights

## Context & Problem

In refactoring the Update Data module, we evaluated the roles, naming, and placement of UI orchestration components ("managers"/controllers) – especially regarding file source and archive selection logic. The goal was to clarify architecture, reduce coupling, and make responsibilities explicit.

---

## Key Insights & Decisions

### 1. **Manager/Controller Naming**
- Generic names like `Manager` are discouraged. Use explicit names reflecting UI orchestration roles: e.g., `FilePanelController`, `SourceSelectionController`, `ArchiveSelectionController`.
- Folder should be renamed from `managers` to `controllers` or `ui_controllers` for clarity.

### 2. **Single vs. Split Controllers**
- When two UI groups (e.g., source and archive) are interdependent (such as supporting a "same as source" option), keeping their logic in a single controller simplifies coordination.
- This avoids unnecessary UI events/signals and keeps logic direct and maintainable.
- Only split controllers if their logic becomes truly independent.

### 3. **Controller Placement**
- **Widget-specific, thin, stateless controllers**: Place with the widget (e.g., `components/source_options_group_controller.py`).
- **UI orchestration/stateful/multi-widget controllers**: Place in `controllers/` or `ui_controllers/` folder.
- In our codebase, source/archive selection logic is not widget-specific; it coordinates state and logic across widgets, so it belongs in `controllers/`.

### 4. **Naming Examples**
| Current Name           | Recommended Name                |
|------------------------|---------------------------------|
| file_manager.py        | file_panel_controller.py        |
| file_list_manager.py   | file_list_controller.py         |
| processing_manager.py  | processing_controller.py        |
| state_manager.py       | ui_state_controller.py          |
| managers/ (folder)     | controllers/ or ui_controllers/ |

- For combined source/archive logic: `FileLocationController` or `SourceAndArchiveController`.

### 5. **Design Rationale**
- Centralizing interdependent logic (e.g., source/archive selection) in one controller is a pragmatic, maintainable pattern.
- Reduces event boilerplate and indirection.
- Matches MVP/MVC best practice for composite UI features.

---

## Conclusion
- Use explicit, role-reflecting names for all UI orchestration components.
- Place controllers according to their scope: widget-specific with the widget, orchestration/stateful in `controllers/`.
- Keep interdependent logic together unless/until independence is needed.
- This approach keeps architecture clean, discoverable, and easy to maintain.
