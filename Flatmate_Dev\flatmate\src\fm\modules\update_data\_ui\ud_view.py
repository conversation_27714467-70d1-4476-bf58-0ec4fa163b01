"""
Update Data view implementation.
"""

import os
from abc import ABC, abstractmethod
from pathlib import Path
from typing import List, Optional, Dict, Any

import pandas as pd
from PySide6.QtCore import Signal
from PySide6.QtWidgets import QFileDialog, QMessageBox

from fm.core.services.event_bus import global_event_bus
from fm.core.services.logger import log
from fm.modules.base.base_module_view import BaseModuleView
from ._view.center_panel_layout import CenterPanelManager
from ._view.left_panel_layout import LeftPanelManager
from ..services.local_event_bus import (
    ViewEvents,
    update_data_local_bus,
)
from .ui_events import *
from .interface import IUpdateDataView


# Interface now imported from separate package to avoid circular dependencies


class UpdateDataView(BaseModuleView):
    """
    Update Data view.

    Implements IUpdateDataView interface methods for presenter interaction.
    Note: Does not inherit from Protocol to avoid metaclass conflicts with Qt.
    """
    # ------------------
    # Signals (Interface Implementation)
    # ------------------
    cancel_clicked = Signal()
    source_select_requested = Signal(str)  # selection_type: "folder" or "files"
    save_select_requested = Signal()
    source_option_changed = Signal(str)
    save_option_changed = Signal(str)
    process_clicked = Signal()
    update_database_changed = Signal(bool)


    def __init__(self, parent=None, gui_config=None, gui_keys=None):
        """Initialize the view.

        Args:
            parent: Parent widget
            gui_config: Injected GUI configuration service
            gui_keys: Injected GUI configuration keys
        """
        # Initialize basic properties first
        self.event_bus = global_event_bus
        # MIGRATION: Add local event bus for internal coordination
        self.local_bus = update_data_local_bus
        # Then call parent constructor which will call setup_ui
        super().__init__(parent, gui_config, gui_keys)

    def setup_ui(self):
        """Initial UI setup - called by base class."""
        # MIGRATION: Create proper panel managers instead of direct widgets
        self.left_panel_manager = LeftPanelManager()
        self.center_display = CenterPanelManager()

        # Create guide pane for contextual feedback
        self._create_guide_pane()

        # MIGRATION: Subscribe to events instead of just connecting signals
        self._subscribe_to_events()
        self._connect_signals()

    def setup_left_panel(self, layout):
        """Set up the left panel content in the base class container."""
        # Add the left panel manager to the container layout
        layout.addWidget(self.left_panel_manager)

    def _subscribe_to_events(self):
        """
        Subscribe to local events for UI updates.

        MIGRATION: View now reacts to events instead of direct method calls.
        """
        self.local_bus.subscribe(ViewEvents.UI_STATE_CHANGED.value, self.update_ui_state)
        self.local_bus.subscribe(ViewEvents.STATUS_MESSAGE_CHANGED.value, self.update_status_message)
        self.local_bus.subscribe(ViewEvents.FILE_DISPLAY_UPDATED.value, self.update_files_display)

    def _connect_signals(self):
        """
        Connect internal signals to be forwarded.

        MIGRATION: Now also emits local events alongside Qt signals.
        """
        # Connect to left panel manager signals (NO MORE buttons_widget!)
        self.left_panel_manager.source_select_requested.connect(self.source_select_requested.emit)
        self.left_panel_manager.save_select_requested.connect(self.save_select_requested.emit)
        self.left_panel_manager.process_clicked.connect(self.process_clicked.emit)
        self.left_panel_manager.cancel_clicked.connect(self.cancel_clicked.emit)

        # Option change signals
        self.left_panel_manager.source_option_changed.connect(self.source_option_changed.emit)
        self.left_panel_manager.save_option_changed.connect(self.save_option_changed.emit)
        self.left_panel_manager.update_database_changed.connect(self.update_database_changed.emit)

    def _emit_user_action(self, event_type: ViewEvents, data: dict):
        """Helper method to emit user action events."""
        self.local_bus.emit(event_type.value, data)

    def _get_process_context(self) -> dict:
        """Get context data for processing request."""
        return {
            'update_database': self.get_update_database(),
            'save_option': self.get_save_option()
        }

    # ------------------
    # Event Reaction Methods (MIGRATION)
    # ------------------

    def update_ui_state(self, ui_state_data):
        """
        React to UI state changes via events.

        MIGRATION: Replaces direct method calls from state coordinator.
        """
        # Update process button
        if 'can_process' in ui_state_data:
            self.set_process_enabled(ui_state_data['can_process'])

        # Update archive section
        if 'archive_enabled' in ui_state_data:
            self.set_archive_enabled(ui_state_data['archive_enabled'])

        # Update process button text
        if 'process_button_text' in ui_state_data:
            self.set_process_text(ui_state_data['process_button_text'])

        # Update processing state
        if 'processing' in ui_state_data:
            self.set_all_controls_enabled(not ui_state_data['processing'])

    def update_status_message(self, message_data):
        """
        React to status message changes via events.

        MIGRATION: Replaces direct guide pane calls.
        """
        if hasattr(self, 'guide_pane') and 'message' in message_data:
            self.guide_pane.display(message_data['message'])

    def update_files_display(self, files_data):
        """
        React to files display updates via events.

        MIGRATION: New event-driven file display updates.
        """
        log.debug(f"[UD_VIEW] Received FILE_DISPLAY_UPDATED event: {type(files_data)}")

        # Handle both dict and dataclass event data
        if hasattr(files_data, 'files') and hasattr(files_data, 'source_path'):
            # Dataclass format (FileDisplayUpdateEvent)
            log.debug(f"[UD_VIEW] Processing dataclass format - files: {len(files_data.files)}, source_path: {files_data.source_path}")
            self.center_display.set_files(files_data.files, files_data.source_path)
            log.debug(f"[UD_VIEW] Called center_display.set_files() with dataclass data")
        elif 'files' in files_data and 'source_path' in files_data:
            # Dict format (legacy)
            log.debug(f"[UD_VIEW] Processing dict format - files: {len(files_data['files'])}, source_path: {files_data['source_path']}")
            self.center_display.set_files(files_data['files'], files_data['source_path'])
            log.debug(f"[UD_VIEW] Called center_display.set_files() with dict data")
        else:
            log.warning(f"[UD_VIEW] Unknown files_data format: {files_data}")

    # setup_left_panel method already defined above - removing duplicate

    def setup_center_panel(self, layout):
        """Set up the center panel with display areas."""
        layout.addWidget(self.center_display)

    def disconnect_signals(self):
        """Clean up signal connections."""
        if hasattr(self, "left_panel_manager"):
            # Disconnect from panel manager signals
            if hasattr(self.left_panel_manager, 'publish_file_selected'):
                self.left_panel_manager.publish_file_selected.disconnect()
            if hasattr(self.left_panel_manager, 'publish_data_selected'):
                self.left_panel_manager.publish_data_selected.disconnect()
            if hasattr(self.left_panel_manager, 'publish_exit_selected'):
                self.left_panel_manager.publish_exit_selected.disconnect()

    # ------------------
    # UI State Methods
    # ------------------
    def set_exit_mode(self):
        """Set left panel to exit mode."""
        self.left_panel_manager.set_exit_mode()

    def set_process_mode(self):
        """Set left panel to process mode."""
        self.left_panel_manager.set_process_mode()

    def set_save_select_enabled(self, enabled: bool):
        """Enable/disable save select button."""
        self.left_panel_manager.set_archive_enabled(enabled)

    def get_update_database(self) -> bool:
        """Get the current state of the update database checkbox."""
        return self.left_panel_manager.get_update_database()

    def set_source_option(self, option: str):
        """Set the source option in the left panel."""
        self.left_panel_manager.set_source_option(option)

    def set_update_database(self, checked: bool):
        """Set the state of the update database checkbox."""
        # TODO: Add this method to left panel manager
        pass

    # ------------------
    # SimpleStateCoordinator Interface Methods
    # MIGRATION: These methods are now called via events (update_ui_state)
    # but kept for backward compatibility during migration
    # ------------------
    def set_process_enabled(self, enabled: bool):
        """Enable/disable the process button."""
        print(f"[STATE_COORDINATOR] Setting process button enabled: {enabled}")  # Debug logging
        self.left_panel_manager.set_process_button_enabled(enabled)

    def set_archive_enabled(self, enabled: bool):
        """Enable/disable the archive section."""
        print(f"[STATE_COORDINATOR] Setting archive section enabled: {enabled}")  # Debug logging
        self.set_save_select_enabled(enabled)

    def set_process_text(self, text: str):
        """Set the process button text."""
        print(f"[STATE_COORDINATOR] Setting process button text: {text}")  # Debug logging
        self.left_panel_manager.set_process_button_text(text)

    def set_all_controls_enabled(self, enabled: bool):
        """Enable/disable all controls during processing."""
        self.left_panel_manager.set_process_button_enabled(enabled)
        # TODO: Add methods to enable/disable source and archive selection
        # self.left_panel_manager.set_source_selection_enabled(enabled)
        # self.left_panel_manager.set_archive_selection_enabled(enabled)

    # ------------------
    # Display Methods - interface for presenter
    # ------------------

    def set_save_path(self, path: str):
        """Set the save location path in the center panel."""
        self.center_display.set_save_path(path)

    def display_selected_source(self, source_info: dict):
        """Display the selected source files in the center panel."""
        if not source_info:
            return

        if source_info["type"] == "folder":
            self.center_display.set_source_path(source_info["path"])
            # Pass the full file paths to the file browser
            self.center_display.set_files(
                source_info["file_paths"], source_info["path"]
            )
        else:  # files
            files = source_info["file_paths"]
            source_dir = os.path.dirname(files[0])
            self.center_display.set_source_path(source_dir)
            # Pass the full file paths to the file browser
            self.center_display.set_files(files, source_dir)
            
    def display_enriched_file_info(self, file_info_list: List[Dict[str, Any]]) -> None:
        """Display enriched file information received from the presenter."""
        if not file_info_list:
            return
            
        # Log the enriched file info
        log.debug(f"Displaying enriched file info: {file_info_list}")
        
        # Update the file display with enriched information
        # This will be delegated to the center panel manager
        self.center_display.display_enriched_file_info(file_info_list)

    def display_master_csv(self, df: pd.DataFrame):
        """Display a DataFrame in the center panel table."""
        self.center_display.display_master_csv(df)

    def display_welcome(self):
        """Display welcome message in center panel."""
        self.center_display.display_welcome()

    # ------------------
    # Info Methods
    # ------------------
    # Status bar-related methods removed as they're redundant
    # The presenter should use InfoBarService directly or publish events
    # through the event bus. The UpdateDataStatusBar now subscribes to
    # these events directly.

    # ------------------
    # Dialog Methods
    # ------------------
    def show_error(self, message: str, title: str = "Error"):
        """Show error message box."""
        QMessageBox.critical(self, title, message)

    def show_success(self, message: str, title: str = "Success"):
        """Show success message box."""
        QMessageBox.information(self, title, message)

    def get_save_option(self) -> str:
        """Get the current save option from the left panel buttons."""
        return self.left_panel_manager.get_archive_option()

    def show_folder_dialog(self, title: str, initial_dir: Optional[str] = None) -> str:
        """Show a folder selection dialog and return the selected folder path."""
        # Use proper folder dialog on all platforms
        return QFileDialog.getExistingDirectory(
            self,
            title,
            initial_dir or str(Path.home()),
            QFileDialog.Option.ShowDirsOnly
        )

    def show_files_dialog(
        self,
        title: str,
        initial_dir: Optional[str] = None,
        filter_str: str = "Data Files (*.csv *.CSV)",
    ) -> List[str]:
        """Show a file selection dialog and return the selected file paths."""
        files, _ = QFileDialog.getOpenFileNames(
            self, title, initial_dir or "", filter_str
        )
        return files

    def _create_guide_pane(self):
        """Create guide pane with lazy import to avoid circular dependencies."""
        from ._view.center_panel_components.guide_pane import GuidePaneWidget
        self.guide_pane = GuidePaneWidget()

    def set_process_button_text(self, text: str) -> None:
        """Set process button text."""
        self.left_panel_manager.set_process_button_text(text)

    def set_guide_content(self, content: str) -> None:
        """Emergency compatibility method - delegates to guide pane display."""
        if hasattr(self, 'guide_pane') and self.guide_pane:
            self.guide_pane.display(content, 'text')

    def show_folder_dialog(self, title: str, initial_dir: str) -> str:
        """Show folder selection dialog."""
        folder = QFileDialog.getExistingDirectory(self, title, initial_dir)
        return folder if folder else ""

    def display_selected_source(self, source_data: Dict[str, Any]) -> None:
        """Display selected source information."""
        # TODO: Implement source display in center panel
        pass

    def cleanup(self) -> None:
        """Clean up view resources."""
        # Disconnect signals and clean up
        try:
            self._disconnect_signals()
        except:
            pass

    def show_component(self) -> None:
        """Show the view component."""
        self.show()

    def hide_component(self) -> None:
        """Hide the view component."""
        self.hide()

    def connect_file_list_manager(self, local_bus) -> None:
        """Connect file pane to FileListManager events."""
        try:
            if hasattr(self.center_display, 'file_pane'):
                self.center_display.file_pane.connect_to_file_list_manager(local_bus)
                log.debug("[UD_VIEW] Connected file pane to FileListManager events")
        except Exception as e:
            log.error(f"[UD_VIEW] Error connecting file list manager: {e}")

    def get_current_files(self) -> List[str]:
        """Get the current list of files from the file pane."""
        try:
            if hasattr(self.center_display, 'file_pane_v2'):
                return self.center_display.file_pane_v2.get_files()
            elif hasattr(self.center_display, 'file_pane'):
                return self.center_display.file_pane.get_files()
            return []
        except Exception as e:
            log.error(f"[UD_VIEW] Error getting current files: {e}")
            return []
    
    # === FILE OPERATIONS (New file_pane_v2 support) ===
    def add_files(self, files: List[str]) -> None:
        """Add files to the file view."""
        try:
            if hasattr(self.center_display, 'file_pane_v2'):
                for file_path in files:
                    self.center_display.file_pane_v2.add_file(file_path)
                log.debug(f"[UD_VIEW] Added {len(files)} files to file_pane_v2")
            else:
                log.warning("[UD_VIEW] file_pane_v2 not available for add_files")
        except Exception as e:
            log.error(f"[UD_VIEW] Error adding files: {e}")
    
    def remove_file(self, file_path: str) -> None:
        """Remove a file from the file view."""
        try:
            if hasattr(self.center_display, 'file_pane_v2'):
                self.center_display.file_pane_v2.remove_file(file_path)
                log.debug(f"[UD_VIEW] Removed file: {file_path}")
            else:
                log.warning("[UD_VIEW] file_pane_v2 not available for remove_file")
        except Exception as e:
            log.error(f"[UD_VIEW] Error removing file: {e}")
    
    def set_files(self, files: List[str]) -> None:
        """Set the complete list of files in the file view."""
        try:
            if hasattr(self.center_display, 'file_pane_v2'):
                self.center_display.file_pane_v2.set_files(files)
                log.debug(f"[UD_VIEW] Set {len(files)} files in file_pane_v2")
            else:
                log.warning("[UD_VIEW] file_pane_v2 not available for set_files")
        except Exception as e:
            log.error(f"[UD_VIEW] Error setting files: {e}")
    
    def get_selected_file(self) -> str:
        """Get the currently selected file path."""
        try:
            if hasattr(self.center_display, 'file_pane_v2'):
                selected = self.center_display.file_pane_v2.get_selected_file()
                return selected if selected else ""
            return ""
        except Exception as e:
            log.error(f"[UD_VIEW] Error getting selected file: {e}")
            return ""
    
    def clear_files(self) -> None:
        """Clear all files from the file view."""
        try:
            if hasattr(self.center_display, 'file_pane_v2'):
                self.center_display.file_pane_v2.clear_files()
                log.debug("[UD_VIEW] Cleared all files from file_pane_v2")
            else:
                log.warning("[UD_VIEW] file_pane_v2 not available for clear_files")
        except Exception as e:
            log.error(f"[UD_VIEW] Error clearing files: {e}")


