"""Center panel manager for the Update Data module."""


from fm.core.services.event_bus import Events, global_event_bus
from PySide6.QtCore import Signal
from PySide6.QtWidgets import QVBoxLayout, QSplitter
from PySide6.QtCore import Qt
import pandas as pd

from fm.gui._shared_components.base.base_panel_component import BasePanelComponent

from .center_panel_components import guide_pane
from .center_panel_components.file_pane_v2 import UDFileView
from .center_panel_components.old_files_backup.file_pane import FilePane

from fm.core.services.logger import log


class CenterPanelManager(BasePanelComponent):
    """Main center panel manager for the Update Data module.

    This class uses the Composite pattern to manage different panes
    that can be shown in the center area of the Update Data module.
    """

    # Signals for publishing events to subscribers
    publish_file_removed = Signal(str)  # Publishes path of removed file
    publish_file_selected = Signal(str)  # Publishes path of selected file

    # Pane identifiers
    WELCOME_PANE = "welcome"
    FILE_PANE = "file"
    DATA_PANE = "data"

    def __init__(self, parent=None):
        """Initialize the center panel manager."""
        super().__init__(parent)
        self.info_bar = None
        self.event_bus = global_event_bus
        self._init_ui()
        self._connect_signals()

    def _init_ui(self):
        """Initialize the UI components with resizable split layout."""
        # Main layout
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)

        # Create vertical splitter for guide pane (top) and file display (bottom)
        self.splitter = QSplitter(Qt.Vertical, self)
        self.main_layout.addWidget(self.splitter)

        self._create_panes()

        # Set default split ratio (guide pane gets 40%, file display gets 60%)
        self.splitter.setSizes([400, 600])  # Proportional sizes

    def _create_panes(self):
        """Create panes and add them to the splitter."""
        # Guide pane (top half) - contextual messages and instructions
        self.guide_pane = guide_pane.GuidePaneWidget()
        self.splitter.addWidget(self.guide_pane)

        # File display pane (bottom half) - file list and management
        self.file_pane = FilePane()
        self.splitter.addWidget(self.file_pane)
        
        # New file_pane_v2 component (parallel implementation)
        self.file_pane_v2 = UDFileView()
        # Initially hidden - will be shown when switching to v2
        self.file_pane_v2.hide()
      

    def _connect_signals(self):
        """Connect signals between components."""
        # Connect original file_pane signals
        self.file_pane.publish_file_removed.connect(self.publish_file_removed.emit)
        self.file_pane.publish_file_selected.connect(self.publish_file_selected.emit)
        
        # Connect new file_pane_v2 signals
        self.file_pane_v2.file_selected.connect(self.publish_file_selected.emit)
        self.file_pane_v2.file_list_changed.connect(self._on_file_list_changed)
        self.file_pane_v2.processing_requested.connect(self._on_processing_requested)
    
    def _on_file_list_changed(self, file_paths: list) -> None:
        """Handle file list changes from file_pane_v2."""
        log.debug(f"[CENTER_PANEL] File list changed: {len(file_paths)} files")
        # Could emit additional signals or update guide pane
    
    def _on_processing_requested(self) -> None:
        """Handle processing request from file_pane_v2."""
        log.debug("[CENTER_PANEL] Processing requested from file_pane_v2")
        # Could emit a signal to trigger processing
    
    def use_file_pane_v2(self, enable: bool = True) -> None:
        """Switch between file_pane and file_pane_v2."""
        if enable:
            self.file_pane.hide()
            self.file_pane_v2.show()
            log.debug("[CENTER_PANEL] Switched to file_pane_v2")
        else:
            self.file_pane_v2.hide()
            self.file_pane.show()
            log.debug("[CENTER_PANEL] Switched to original file_pane")
        

    def set_source_path(self, path: str):
        """Set the source folder path."""
        # Access the file pane directly
        self.file_pane.set_source_path(path)

    def set_save_path(self, path: str):
        """Set the save location path."""
        # Access the file pane directly
        self.file_pane.set_save_path(path)

    def set_files(self, files: list, source_dir: str = ""):
        """Set the files to display in the file pane.

        Args:
            files: List of file paths
            source_dir: Source directory for relative paths
        """
        log.debug(f"[CENTER_PANEL] Received set_files call - files: {len(files) if files else 0}, source_dir: {source_dir}")
        log.debug(f"[CENTER_PANEL] Files list: {files}")

        # File pane is always visible in the bottom half
        log.debug(f"[CENTER_PANEL] Calling file_pane.set_files()")
        self.file_pane.set_files(files, source_dir)
        log.debug(f"[CENTER_PANEL] file_pane.set_files() completed")

        # Update guide pane with file selection information
        if files and len(files) > 0:
            log.debug(f"[CENTER_PANEL] Updating guide pane with {len(files)} files")
            # Update guide pane state with file count
            self.guide_pane.set_state('files_selected', {'count': len(files)})
            log.debug(f"[CENTER_PANEL] Guide pane updated")
            
            # If we have a source directory, show folder monitoring option
            if source_dir and len(source_dir) > 0:
                self.guide_pane.show_folder_monitoring_option(False)

    def get_files(self) -> list[str]:
        """Get all file paths in the tree."""
        return self.file_pane.get_files()

    def display_enriched_file_info(self, file_info_list):
        """Display enriched file information in the file pane.
        
        Args:
            file_info_list: List of dictionaries with enriched file information
        """
        if not file_info_list:
            return
            
        # Delegate to file pane to display enriched file info
        self.file_pane.display_enriched_file_info(file_info_list)
        
        # Update guide pane with file info summary
        self.guide_pane.set_state('files_enriched', {
            'count': len(file_info_list),
            'types': set(info.get('format_type', 'Unknown') for info in file_info_list)
        })
    
    def display_master_csv(self, df: pd.DataFrame, file_info: str = ""):
        """Display a DataFrame in a table.

        Args:
            df: DataFrame to display
            file_info: Information about the file being displayed
        """
        # TODO: Data pane deprecated - need to implement with new shared table_view component
        # self.data_pane.display_dataframe(df, file_info)  # data_pane is in archive
        # self.show_data_pane()  # Method still exists but pane is missing

        # Temporary: Show in guide pane or implement new table view
        print(f"TODO: Display DataFrame with {len(df)} rows - {file_info}")
        # Consider using: from fm.gui._shared_components.widgets.table_view import TableView

    def display_welcome(self):
        """Display welcome message in guide pane."""
        # Reset guide pane to initial state (always visible in top half)
        self.guide_pane.reset_to_initial()
        # Clear file pane (always visible in bottom half)
        self.file_pane.clear()

    def show_error(self, message: str):
        """Show error message in guide pane."""
        self.guide_pane.show_error(message)
        # Also publish to event bus for InfoBar to pick up
        self.event_bus.publish(Events.INFO_MESSAGE, f"Error: {message}")

    def show_success(self, message: str):
        """Show success message in guide pane."""
        self.guide_pane.show_success(message)
        # Also publish to event bus for InfoBar to pick up
        self.event_bus.publish(Events.INFO_MESSAGE, message)
