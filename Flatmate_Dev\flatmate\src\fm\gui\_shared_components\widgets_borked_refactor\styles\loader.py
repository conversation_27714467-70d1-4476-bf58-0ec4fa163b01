"""
Style Loader

Centralized style loading for widgets with caching and widget-specific loading.
"""

from pathlib import Path
from typing import Op<PERSON>, Dict
from PySide6.QtWidgets import QWidget, QApplication


class StyleLoader:
    """Centralized style loading for widgets."""
    
    _styles_cache: Dict[str, str] = {}
    _styles_dir = Path(__file__).parent
    
    @classmethod
    def load_base_styles(cls) -> str:
        """Load base styles for all widgets.
        
        Returns:
            Base CSS styles as string
        """
        return cls._load_style_file("base.qss")
    
    @classmethod
    def load_widget_styles(cls, widget_type: str) -> str:
        """Load styles for specific widget type.
        
        Args:
            widget_type: Type of widget (e.g., 'buttons', 'checkboxes')
            
        Returns:
            Widget-specific CSS styles as string
        """
        style_file = f"widgets/{widget_type}.qss"
        return cls._load_style_file(style_file)
    
    @classmethod
    def apply_widget_styles(cls, widget: QWidget, widget_type: str):
        """Apply styles to widget instance.
        
        Args:
            widget: Widget instance to style
            widget_type: Type of widget for style selection
        """
        base_styles = cls.load_base_styles()
        widget_styles = cls.load_widget_styles(widget_type)
        
        combined_styles = f"{base_styles}\n{widget_styles}"
        if combined_styles.strip():
            widget.setStyleSheet(combined_styles)
    
    @classmethod
    def _load_style_file(cls, filename: str) -> str:
        """Load and cache style file.
        
        Args:
            filename: Relative path to style file
            
        Returns:
            Style content as string
        """
        if filename in cls._styles_cache:
            return cls._styles_cache[filename]
        
        style_path = cls._styles_dir / filename
        if style_path.exists():
            try:
                with open(style_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    cls._styles_cache[filename] = content
                    return content
            except Exception as e:
                print(f"Warning: Could not load style file {filename}: {e}")
                return ""
        
        return ""
    
    @classmethod
    def clear_cache(cls):
        """Clear style cache (useful for development)."""
        cls._styles_cache.clear()
    
    @classmethod
    def reload_styles(cls):
        """Reload all styles from disk."""
        cls.clear_cache()
        # Trigger style refresh on all widgets
        app = QApplication.instance()
        if app:
            app.setStyleSheet(app.styleSheet())
    
    @classmethod
    def create_style_files(cls):
        """Create empty style files if they don't exist (development helper)."""
        base_file = cls._styles_dir / "base.qss"
        if not base_file.exists():
            base_file.touch()
        
        widgets_dir = cls._styles_dir / "widgets"
        widgets_dir.mkdir(exist_ok=True)
        
        widget_types = ['buttons', 'checkboxes', 'labels', 'option_menus', 'selectors', 'filters']
        for widget_type in widget_types:
            style_file = widgets_dir / f"{widget_type}.qss"
            if not style_file.exists():
                style_file.touch()
