"""
File table widget for displaying file information.

Internal component for the file view widget.
"""

from PySide6.QtWidgets import (
    QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QIcon
from typing import List, Optional
from ..models import FileInfo, FileViewModel
from ..config import FileConfig
from ..utils import get_file_icon_name, format_file_size
from fm.core.services.logger import log


class FileTable(QTableWidget):
    """Table widget for displaying file information."""
    
    # Signals
    file_selected = Signal(str)  # file_path
    file_double_clicked = Signal(str)  # file_path
    
    def __init__(self, model: FileViewModel, config: FileConfig, parent=None):
        super().__init__(parent)
        self._model = model
        self._config = config
        self._setup_table()
        self._connect_signals()
    
    def _setup_table(self) -> None:
        """Set up the table widget."""
        # Define columns
        self._columns = []
        if self._config.show_file_icons:
            self._columns.append("Icon")
        self._columns.extend(["Name", "Path"])
        
        if self._config.show_file_size:
            self._columns.append("Size")
        if self._config.show_file_type:
            self._columns.append("Type")
        if self._config.show_modified_date:
            self._columns.append("Modified")
        
        # Set up table
        self.setColumnCount(len(self._columns))
        self.setHorizontalHeaderLabels(self._columns)
        
        # Configure table behaviour
        self.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.setAlternatingRowColors(self._config.table_alternating_row_colours)
        self.setSortingEnabled(True)
        
        # Configure headers
        if not self._config.table_header_visible:
            self.horizontalHeader().setVisible(False)
        
        # Set column resize modes
        header = self.horizontalHeader()
        header.setStretchLastSection(True)
        
        # Set specific column widths
        if "Icon" in self._columns:
            header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
            self.setColumnWidth(0, 32)
        
        name_col = self._columns.index("Name")
        header.setSectionResizeMode(name_col, QHeaderView.ResizeMode.Stretch)
    
    def _connect_signals(self) -> None:
        """Connect internal signals."""
        self.itemSelectionChanged.connect(self._on_selection_changed)
        self.itemDoubleClicked.connect(self._on_item_double_clicked)
    
    def refresh_data(self) -> None:
        """Refresh table data from the model."""
        try:
            self.setRowCount(len(self._model.files))
            
            for row, file_info in enumerate(self._model.files):
                self._populate_row(row, file_info)
            
            # Restore selection if possible
            if self._model.selected_file:
                self._select_file_by_path(self._model.selected_file)
        
        except Exception as e:
            log.error(f"Error refreshing file table data: {e}")
    
    def _populate_row(self, row: int, file_info: FileInfo) -> None:
        """Populate a single row with file information."""
        col = 0
        
        # Icon column
        if "Icon" in self._columns:
            icon_item = QTableWidgetItem()
            icon_name = get_file_icon_name(file_info.path)
            # Note: Icon loading would need proper icon resources
            icon_item.setData(Qt.ItemDataRole.UserRole, file_info.path)
            self.setItem(row, col, icon_item)
            col += 1
        
        # Name column
        name_item = QTableWidgetItem(file_info.name)
        name_item.setData(Qt.ItemDataRole.UserRole, file_info.path)
        self.setItem(row, col, name_item)
        col += 1
        
        # Path column
        path_item = QTableWidgetItem(file_info.path)
        path_item.setData(Qt.ItemDataRole.UserRole, file_info.path)
        self.setItem(row, col, path_item)
        col += 1
        
        # Size column
        if "Size" in self._columns:
            size_item = QTableWidgetItem(file_info.size_formatted)
            size_item.setData(Qt.ItemDataRole.UserRole, file_info.path)
            self.setItem(row, col, size_item)
            col += 1
        
        # Type column
        if "Type" in self._columns:
            type_item = QTableWidgetItem(file_info.file_type)
            type_item.setData(Qt.ItemDataRole.UserRole, file_info.path)
            self.setItem(row, col, type_item)
            col += 1
        
        # Modified column
        if "Modified" in self._columns:
            modified_str = file_info.modified.strftime("%Y-%m-%d %H:%M")
            modified_item = QTableWidgetItem(modified_str)
            modified_item.setData(Qt.ItemDataRole.UserRole, file_info.path)
            self.setItem(row, col, modified_item)
            col += 1
    
    def _select_file_by_path(self, file_path: str) -> None:
        """Select a file by its path."""
        for row in range(self.rowCount()):
            item = self.item(row, 0)
            if item and item.data(Qt.ItemDataRole.UserRole) == file_path:
                self.selectRow(row)
                break
    
    def _on_selection_changed(self) -> None:
        """Handle selection change."""
        selected_items = self.selectedItems()
        if selected_items:
            file_path = selected_items[0].data(Qt.ItemDataRole.UserRole)
            self._model.set_selected_file(file_path)
            self.file_selected.emit(file_path)
        else:
            self._model.set_selected_file(None)
    
    def _on_item_double_clicked(self, item: QTableWidgetItem) -> None:
        """Handle item double click."""
        file_path = item.data(Qt.ItemDataRole.UserRole)
        if file_path:
            self.file_double_clicked.emit(file_path)
    
    def get_selected_file_path(self) -> Optional[str]:
        """Get the currently selected file path."""
        return self._model.selected_file
