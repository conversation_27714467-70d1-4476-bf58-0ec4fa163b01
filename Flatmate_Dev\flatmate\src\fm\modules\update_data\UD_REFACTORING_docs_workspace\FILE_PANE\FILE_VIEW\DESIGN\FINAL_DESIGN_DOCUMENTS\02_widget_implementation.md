# File View Widget Implementation

## Overview

This document defines the implementation details for the File View widget component. It provides concrete decisions on the widget's architecture, responsibilities, and integration with the Update Data module.

## Smart Widget Pattern

The File View will follow the **Smart Widget Pattern**, which means:

1. **Self-Contained**: The widget manages its own internal state and UI operations
2. **Clean API**: Exposes high-level methods for external components to interact with
3. **Event-Based Communication**: Uses events for truly asynchronous operations
4. **Internal State Management**: Handles its own data model and UI updates

## Component Architecture

### Class Hierarchy

```
BasePane
   └── UDFileView (Main component)
         ├── FileTable (Core file display)
         ├── AddRemoveButtons (Add/Remove functionality)
         └── ContextMenu (Right-click menu)
```

### Component Responsibilities

1. **UDFileView** (in `ud_file_view.py`)
   - Main component that implements the smart widget pattern
   - Provides a clean API for the presenter to interact with
   - Manages internal state and configuration
   - Composes internal components (file table, add/remove buttons)

2. **FileTable** (in `components/file_table.py`)
   - Core file display widget based on the existing implementation
   - Handles file list display and selection
   - Emits signals for file selection

3. **AddRemoveButtons** (in `components/add_remove_btns.py`)
   - Container for Add/Remove buttons functionality
   - Emits signals for add/remove actions

4. **ContextMenu** (in `components/context_menu.py`)
   - Right-click context menu implementation
   - Provides file operations through context menu

5. **FileViewModel** (in `models.py`)
   - Data model for the file view component
   - Manages the list of files and their metadata
   - Tracks selection state

6. **FileConfig** (in `config.py`)
   - Configuration dataclass for file view behavior
   - Focused on practical configuration needs

## Data Model

```python
# models.py
from dataclasses import dataclass
from typing import List, Optional
from datetime import datetime

@dataclass
class FileInfo:
    """Data structure for file information."""
    path: str
    size: int
    modified: datetime
    file_type: str
    is_valid: bool = True
    is_processed: bool = False
    
class FileViewModel:
    """Data model for the file view component."""
    
    def __init__(self):
        self.files: List[FileInfo] = []
        self.selected_file: Optional[str] = None
        
    def add_file(self, file_path: str) -> FileInfo:
        """Add a file to the model."""
        file_info = self._create_file_info(file_path)
        self.files.append(file_info)
        return file_info
        
    def remove_file(self, file_path: str) -> None:
        """Remove a file from the model."""
        self.files = [f for f in self.files if f.path != file_path]
        if self.selected_file == file_path:
            self.selected_file = None
        
    def _create_file_info(self, file_path: str) -> FileInfo:
        """Create a FileInfo object with metadata."""
        # Extract file metadata using file_info_service
        return FileInfo(
            path=file_path,
            size=os.path.getsize(file_path),
            modified=datetime.fromtimestamp(os.path.getmtime(file_path)),
            file_type=os.path.splitext(file_path)[1],
            is_valid=True,
            is_processed=False
        )
```

## Configuration

```python
# config.py
from dataclasses import dataclass, field
from typing import List

@dataclass
class FileConfig:
    """Configuration for file view behavior and appearance."""
    
    # Display options
    show_file_icons: bool = True
    show_file_size: bool = True
    show_file_type: bool = True
    
    # File display options
    group_by_folder: bool = True
    sort_by: str = "name"  # "name", "size", "type", "date"
    sort_order: str = "asc"  # "asc", "desc"
    
    # File operations
    allow_add: bool = True
    allow_remove: bool = True
    allow_context_menu: bool = True
    
    # File types
    allowed_file_types: List[str] = field(
        default_factory=lambda: ["*.csv"]  # Default to CSV files only
    )
```

## Main Component API

```python
# ud_file_view.py
from PySide6.QtWidgets import QVBoxLayout
from fm.gui.components.shared.base_pane import BasePane
from fm.modules.update_data.ui.events.file_events import FileViewEvents
from .models import FileViewModel
from .config import FileConfig
from .components.file_table import FileTable
from .components.add_remove_btns import AddRemoveButtons

class UDFileView(BasePane):
    """Self-contained file display component inheriting from BasePane."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.events = FileViewEvents()  # EXPLICIT event object
        self._model = FileViewModel()
        self._config = FileConfig()
        self._file_info_service = self._get_file_info_service()
        self._file_dialog_service = self._get_file_dialog_service()
        
        # Initialize layout
        self._init_layout()
        self._setup_ui()
        self._connect_signals()
        
    # Public API methods (interface)
    
    def configure(self, **kwargs) -> 'UDFileView':
        """Configure component behavior."""
        for key, value in kwargs.items():
            if hasattr(self._config, key):
                setattr(self._config, key, value)
        self._apply_configuration()
        return self  # For method chaining
        
    def set_files(self, files: List[str]) -> 'UDFileView':
        """Set the files to display."""
        self._model.files.clear()
        for file in files:
            self._model.add_file(file)
        self._refresh_view()
        self.events.file_paths_list_updated.emit(self.get_files())
        return self
        
    def add_file(self, file_path: str) -> 'UDFileView':
        """Add a single file to the view."""
        self._model.add_file(file_path)
        self._refresh_view()
        self.events.file_paths_list_updated.emit(self.get_files())
        return self
        
    def remove_file(self, file_path: str) -> 'UDFileView':
        """Remove a file from the view."""
        self._model.remove_file(file_path)
        self._refresh_view()
        self.events.file_paths_list_updated.emit(self.get_files())
        return self
        
    def get_files(self) -> List[str]:
        """Get the current list of files."""
        return [f.path for f in self._model.files]
        
    def get_selected_file(self) -> Optional[str]:
        """Get the currently selected file."""
        return self._model.selected_file
    
    # Internal methods
    
    def _init_layout(self) -> None:
        """Initialize the component layout."""
        self._main_layout = QVBoxLayout()
        self._main_layout.setContentsMargins(0, 0, 0, 0)
        self._main_layout.setSpacing(0)
        self.content_widget.setLayout(self._main_layout)
        
    def _setup_ui(self) -> None:
        """Set up the UI components."""
        self._file_table = FileTable(self)
        self._add_remove_btns = AddRemoveButtons(self)
        
        self._main_layout.addWidget(self._file_table)
        self._main_layout.addWidget(self._add_remove_btns)
        
    def _connect_signals(self) -> None:
        """Connect internal signals."""
        self._add_remove_btns.add_button_clicked.connect(self._handle_add_button_clicked)
        self._add_remove_btns.remove_button_clicked.connect(self._handle_remove_button_clicked)
        self._file_table.file_selected.connect(self._handle_file_selected)
        
    def _handle_add_button_clicked(self) -> None:
        """Handle add button click by opening file dialog directly."""
        files = self._file_dialog_service.get_files(
            file_types=self._config.allowed_file_types
        )
        if files:
            for file in files:
                self.add_file(file)
        
    def _handle_remove_button_clicked(self) -> None:
        """Handle remove button click internally."""
        selected = self._model.selected_file
        if selected:
            self.remove_file(selected)
            
    def _handle_file_selected(self, file_path: str) -> None:
        """Handle file selection."""
        self._model.selected_file = file_path
        self.events.file_selected.emit(file_path)
        
    def _refresh_view(self) -> None:
        """Update the UI with current file list."""
        self._file_table.set_files(self._model.files)
        
    def _get_file_info_service(self):
        """Get or create file info service."""
        from fm.services.file_info_service import FileInfoService
        return FileInfoService()
        
    def _get_file_dialog_service(self):
        """Get or create file dialog service."""
        from fm.services.file_dialog_service import FileDialogService
        return FileDialogService()
```

## Integration with Update Data Module

### In the View

```python
# ui/view/ud_view.py
from fm.modules.update_data.ui.view.components.file_pane_v2.ud_file_view import UDFileView

class UpdateDataView(QWidget):
    # View-level signals for presenter
    file_list_changed = Signal(list)
    file_selected = Signal(str)
    
    def _setup_components(self):
        self.center_panel = CenterPanel()
        self.left_panel = LeftPanel()
        
        # Main layout
        main_layout = QHBoxLayout()
        main_layout.addWidget(self.left_panel)
        main_layout.addWidget(self.center_panel)
        self.setLayout(main_layout)
        
    def _connect_widget_signals(self):
        # Get the file view component
        file_view = self.center_panel.file_view
        
        # Connect widget events to view signals
        file_view.events.file_paths_list_updated.connect(self.file_list_changed.emit)
        file_view.events.file_selected.connect(self.file_selected.emit)
```

### In the Presenter

```python
# ud_presenter.py
class UpdateDataPresenter:
    def __init__(self, view):
        self.view = view
        self._connect_signals()
        
    def _connect_signals(self):
        """Connect signals between view and presenter."""
        # Connect to view signals, not widget signals
        self.view.file_list_changed.connect(self._on_file_list_changed)
        self.view.file_selected.connect(self._on_file_selected)
        
        # Configure the file view component
        file_view = self.view.center_panel.file_view
        file_view.configure(
            group_by_folder=True,
            sort_by="name",
            allowed_file_types=["*.csv"]  # Use module constants
        )
        
    def _on_file_list_changed(self, files):
        """Handle file list changes."""
        # Update presenter state
        self._current_files = files
        # Update other components as needed
        
    def _on_file_selected(self, file_path):
        """Handle file selection."""
        # Update presenter state
        self._selected_file = file_path
        # Update other components as needed
```

## Key Implementation Decisions

### 1. BasePane Inheritance

The UDFileView will inherit from BasePane for these reasons:
- Maintains consistency with other panes in the module
- Provides standardized layout and styling
- Ensures proper integration with the center panel structure
- Reduces code duplication by leveraging existing functionality

### 2. Internal State Management

The component will manage its own internal state:
- File list model with metadata
- Selection state tracking
- Sort order management
- File status tracking (valid/invalid, processed/unprocessed)

### 3. File Operations

The component will handle file operations internally:
- File dialog operations are handled within the component
- File filtering and validation happens within the component
- Basic file operations don't require presenter involvement

### 4. Event Communication

The component will publish events for truly asynchronous operations:
- `file_paths_list_updated` when the file list changes
- `file_selected` when a file is selected

### 5. Interface Methods

The component will expose a clean, high-level API:
- `configure()` for runtime configuration
- `set_files()` for setting the file list
- `add_file()` for adding a single file
- `remove_file()` for removing a file
- `get_files()` for getting the current file list
- `get_selected_file()` for getting the currently selected file

## Testing Strategy

1. **Unit Tests**:
   - Test file model operations
   - Test configuration application
   - Test UI updates

2. **Integration Tests**:
   - Test event propagation
   - Test file operations end-to-end
   - Test presenter integration

3. **Manual Testing**:
   - Verify UI appearance and behavior
   - Test with various file types
   - Test error handling

## Migration Strategy

1. Implement the new UDFileView component in parallel with the existing file pane
2. Create unit tests to ensure functionality matches the existing implementation
3. Integrate the new component into the center panel
4. Update the presenter to use the new component API
5. Remove the old file pane implementation once the new component is fully functional
