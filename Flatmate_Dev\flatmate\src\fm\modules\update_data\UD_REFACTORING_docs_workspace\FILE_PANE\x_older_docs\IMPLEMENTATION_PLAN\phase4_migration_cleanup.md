# Phase 4: Migration and Cleanup

## Objective

Migrate existing functionality from the old file pane implementation to the new component, update references throughout the codebase, and clean up deprecated code.

## Steps

### 4.1 Identify Existing File Pane Usage

Analyze the current codebase to identify all places where the existing file pane is used.

```bash
# Search for file pane references
grep -r "file_pane" src/fm/modules/update_data/
grep -r "FilePaneManager" src/fm/modules/update_data/
grep -r "add_files" src/fm/modules/update_data/
```

Document all usages in a migration checklist:

1. File pane manager class
2. File pane widget class
3. File operations in presenter
4. Signal connections in presenter
5. File operations in view
6. Signal connections in view

### 4.2 Create Migration Script

Create a script to assist with the migration process.

**File: `tools/migration/file_pane_migration.py`**

```python
"""
Migration script for file pane refactoring.
This script helps identify and update references to the old file pane implementation.
"""

import os
import re
import argparse
from pathlib import Path

def find_references(base_dir, patterns):
    """Find references to old file pane implementation."""
    results = {}
    
    for root, _, files in os.walk(base_dir):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                for pattern, description in patterns.items():
                    matches = re.findall(pattern, content)
                    if matches:
                        if file_path not in results:
                            results[file_path] = []
                        results[file_path].append({
                            'pattern': pattern,
                            'description': description,
                            'matches': matches
                        })
    
    return results

def main():
    parser = argparse.ArgumentParser(description='File pane migration helper')
    parser.add_argument('--dir', default='src/fm/modules/update_data',
                        help='Base directory to search')
    args = parser.parse_args()
    
    patterns = {
        r'FilePaneManager': 'File pane manager class',
        r'file_pane': 'File pane reference',
        r'add_files': 'File operation',
        r'remove_file': 'File operation',
        r'files_selected': 'File signal',
        r'file_selected': 'File signal'
    }
    
    results = find_references(args.dir, patterns)
    
    print(f"Found {len(results)} files with references to old file pane implementation:")
    for file_path, matches in results.items():
        print(f"\n{file_path}:")
        for match in matches:
            print(f"  - {match['description']}: {len(match['matches'])} occurrences")
            
if __name__ == '__main__':
    main()
```

### 4.3 Create Migration Tests

Create tests to verify functionality before and after migration.

**File: `tests/modules/update_data/migration/test_file_pane_migration.py`**

```python
import pytest
from PySide6.QtWidgets import QApplication
import os
import tempfile

# Import both old and new implementations
from fm.modules.update_data.managers.file_pane_manager import FilePaneManager  # Old
from fm.modules.update_data.ui.view.components.file_pane.ud_file_view import UDFileView  # New

# Test files
TEST_FILES = [
    os.path.join(tempfile.gettempdir(), "test_file1.csv"),
    os.path.join(tempfile.gettempdir(), "test_file2.csv"),
    os.path.join(tempfile.gettempdir(), "test_file3.csv")
]

# Create test files
def setup_module():
    for file_path in TEST_FILES:
        with open(file_path, 'w') as f:
            f.write("test,data\n1,2\n")

# Clean up test files
def teardown_module():
    for file_path in TEST_FILES:
        if os.path.exists(file_path):
            os.remove(file_path)

@pytest.fixture
def app():
    """Create QApplication instance."""
    return QApplication([])

@pytest.fixture
def old_file_pane(app):
    """Create old file pane instance."""
    return FilePaneManager()

@pytest.fixture
def new_file_view(app):
    """Create new file view instance."""
    return UDFileView()

def test_file_operations_parity(old_file_pane, new_file_view):
    """Test that file operations have parity between old and new implementations."""
    # Test adding files
    old_file_pane.add_files(TEST_FILES)
    new_file_view.add_files(TEST_FILES)
    
    # Check file counts match
    assert len(old_file_pane.get_files()) == len(new_file_view.get_files())
    
    # Test removing a file
    file_to_remove = TEST_FILES[0]
    old_file_pane.remove_file(file_to_remove)
    new_file_view.remove_file(file_to_remove)
    
    # Check file counts still match
    assert len(old_file_pane.get_files()) == len(new_file_view.get_files())
    
    # Check file is removed from both
    assert file_to_remove not in old_file_pane.get_files()
    assert file_to_remove not in new_file_view.get_files()
    
    # Test clearing files
    old_file_pane.clear_files()
    new_file_view.clear_files()
    
    # Check both are empty
    assert len(old_file_pane.get_files()) == 0
    assert len(new_file_view.get_files()) == 0

def test_signal_parity(old_file_pane, new_file_view):
    """Test that signals have parity between old and new implementations."""
    # Set up signal capture for old implementation
    old_signals_received = []
    old_file_pane.file_selected.connect(
        lambda file_path: old_signals_received.append(("file_selected", file_path))
    )
    
    # Set up signal capture for new implementation
    new_signals_received = []
    new_file_view.events.file_selected.connect(
        lambda event: new_signals_received.append(("file_selected", event.file_path))
    )
    
    # Add files to both
    old_file_pane.add_files(TEST_FILES)
    new_file_view.add_files(TEST_FILES)
    
    # Simulate file selection in both
    old_file_pane._on_file_selected(TEST_FILES[0])
    new_file_view._on_file_selected(TEST_FILES[0])
    
    # Check signals were emitted
    assert len(old_signals_received) == 1
    assert len(new_signals_received) == 1
    
    # Check signal data matches
    assert old_signals_received[0][0] == new_signals_received[0][0]  # Signal name
    assert old_signals_received[0][1] == new_signals_received[0][1]  # File path
```

### 4.4 Update Import References

Create a plan to update import references throughout the codebase.

1. Identify all import statements for old file pane components
2. Replace with imports for new components
3. Update variable names and method calls as needed

**Example replacements:**

```python
# Old imports
from fm.modules.update_data.managers.file_pane_manager import FilePaneManager

# New imports
from fm.modules.update_data.ui.view.components.file_pane.ud_file_view import UDFileView
from fm.modules.update_data.ui.view.components.file_pane.factory import create_file_view
```

### 4.5 Update Signal Connections

Create a plan to update signal connections throughout the codebase.

1. Identify all signal connections to old file pane signals
2. Replace with connections to new file view events

**Example replacements:**

```python
# Old signal connections
self._file_pane.file_selected.connect(self._on_file_selected)
self._file_pane.files_added.connect(self._on_files_added)

# New signal connections
self._file_view.events.file_selected.connect(self._on_file_selected)
self._file_view.events.file_list_changed.connect(self._on_file_list_changed)
```

### 4.6 Update Method Calls

Create a plan to update method calls throughout the codebase.

1. Identify all method calls to old file pane methods
2. Replace with calls to new file view methods

**Example replacements:**

```python
# Old method calls
self._file_pane.add_files(files)
self._file_pane.remove_file(file_path)

# New method calls
self._file_view.add_files(files)
self._file_view.remove_file(file_path)
```

### 4.7 Create Adapter Class (Optional)

If needed, create an adapter class to maintain backward compatibility during migration.

**File: `ui/view/components/file_pane/adapter.py`**

```python
from PySide6.QtCore import Signal, QObject
from .ud_file_view import UDFileView
from typing import List, Optional

class FilePaneAdapter(QObject):
    """Adapter class to provide backward compatibility with old file pane API."""
    
    # Old-style signals
    file_selected = Signal(str)
    files_added = Signal(list)
    file_removed = Signal(str)
    files_cleared = Signal()
    
    def __init__(self, file_view: UDFileView):
        """Initialize the adapter."""
        super().__init__()
        self._file_view = file_view
        
        # Connect to new events
        self._file_view.events.file_selected.connect(
            lambda event: self.file_selected.emit(event.file_path)
        )
        self._file_view.events.file_list_changed.connect(
            lambda event: self._on_file_list_changed(event)
        )
        
    def _on_file_list_changed(self, event):
        """Handle file list changed event."""
        # Determine what changed and emit appropriate signal
        # This is a simplification; actual implementation would need to track state
        self.files_added.emit(event.files)
        
    # Method adapters
    
    def add_files(self, files: List[str]) -> None:
        """Add files to the view."""
        self._file_view.add_files(files)
        
    def remove_file(self, file_path: str) -> None:
        """Remove a file from the view."""
        self._file_view.remove_file(file_path)
        self.file_removed.emit(file_path)
        
    def get_files(self) -> List[str]:
        """Get the current list of files."""
        return self._file_view.get_files()
        
    def get_selected_file(self) -> Optional[str]:
        """Get the currently selected file."""
        return self._file_view.get_selected_file()
        
    def clear_files(self) -> None:
        """Clear all files from the view."""
        self._file_view.clear_files()
        self.files_cleared.emit()
```

### 4.8 Deprecate Old Components

Mark old components as deprecated to encourage migration to new components.

**File: `managers/file_pane_manager.py`**

```python
import warnings

class FilePaneManager:
    """
    File pane manager for update data module.
    
    DEPRECATED: Use UDFileView instead.
    """
    
    def __init__(self):
        """Initialize the file pane manager."""
        warnings.warn(
            "FilePaneManager is deprecated. Use UDFileView instead.",
            DeprecationWarning,
            stacklevel=2
        )
        # Rest of implementation...
```

### 4.9 Create Migration Guide

Create a migration guide for developers to follow when updating their code.

**File: `DOCS/migration_guides/file_pane_migration_guide.md`**

```markdown
# File Pane Migration Guide

This guide helps you migrate from the old `FilePaneManager` to the new `UDFileView` component.

## Why Migrate?

The new `UDFileView` component offers several advantages:

- Self-contained component with clean API
- Explicit event system with typed events
- Better separation of concerns
- Improved testability
- Consistent with "smart widget" pattern

## Step-by-Step Migration

### 1. Update Imports

Replace:
```python
from fm.modules.update_data.managers.file_pane_manager import FilePaneManager
```

With:
```python
from fm.modules.update_data.ui.view.components.file_pane.ud_file_view import UDFileView
# Or use the factory
from fm.modules.update_data.ui.view.components.file_pane.factory import create_file_view
```

### 2. Update Instantiation

Replace:
```python
self._file_pane = FilePaneManager()
```

With:
```python
self._file_view = UDFileView()
# Or use the factory
self._file_view = create_file_view(config={
    'columns': ['name', 'file_type', 'size', 'modified'],
    'allowed_file_types': ['*.csv', '*.ofx']
})
```

### 3. Update Signal Connections

Replace:
```python
self._file_pane.file_selected.connect(self._on_file_selected)
self._file_pane.files_added.connect(self._on_files_added)
```

With:
```python
self._file_view.events.file_selected.connect(
    lambda event: self._on_file_selected(event.file_path)
)
self._file_view.events.file_list_changed.connect(
    lambda event: self._on_file_list_changed(event.files)
)
```

### 4. Update Method Calls

Most method calls remain the same:
```python
# Both old and new
self._file_view.add_files(files)
self._file_view.remove_file(file_path)
self._file_view.get_files()
self._file_view.get_selected_file()
self._file_view.clear_files()
```

New methods:
```python
# Configure component
self._file_view.configure(
    columns=['name', 'size', 'modified'],
    show_file_size=True
)

# Set processing state
self._file_view.set_processing_state(True)  # Disable during processing
```

### 5. Update Event Handlers

Old event handlers:
```python
def _on_file_selected(self, file_path):
    # Handle file selection
    pass
```

New event handlers:
```python
def _on_file_selected(self, event):
    # Handle file selection
    file_path = event.file_path
    # Rest of handler...
```

## Using the Adapter (Optional)

If you need to maintain backward compatibility, you can use the adapter:

```python
from fm.modules.update_data.ui.view.components.file_pane.adapter import FilePaneAdapter
from fm.modules.update_data.ui.view.components.file_pane.ud_file_view import UDFileView

# Create new component
file_view = UDFileView()

# Wrap with adapter
file_pane = FilePaneAdapter(file_view)

# Use old API
file_pane.add_files(files)
file_pane.file_selected.connect(self._on_file_selected)
```

## Testing Your Migration

Run the migration tests to verify your code works with the new component:

```bash
python -m pytest tests/modules/update_data/migration/test_file_pane_migration.py
```
```

### 4.10 Remove Deprecated Code

After migration is complete, remove deprecated code.

1. Remove old file pane manager class
2. Remove old file pane widget class
3. Remove any adapter classes
4. Update documentation to reference only new components

## Deliverables

1. Migration script
2. Migration tests
3. Adapter class (optional)
4. Deprecated component markers
5. Migration guide
6. Clean codebase with old components removed

## Testing

Run the migration tests to verify:

1. New component provides all functionality of old component
2. Signal connections work correctly
3. Method calls work correctly
4. No regressions in functionality

## Next Steps

1. Update any remaining documentation to reference the new component
2. Train team members on new component usage
3. Monitor for any issues during transition period
