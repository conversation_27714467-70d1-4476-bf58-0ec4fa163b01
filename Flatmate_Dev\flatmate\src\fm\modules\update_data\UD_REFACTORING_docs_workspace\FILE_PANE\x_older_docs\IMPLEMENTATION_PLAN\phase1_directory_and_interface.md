# Phase 1: Directory Structure and Interface Definition

## Objective

Establish the foundational structure for the file view component refactoring, including directory structure, interface protocol, and event definitions.

## Steps

### 1.1 Create Directory Structure

```bash
# Create the directory structure for the new component
mkdir -p src/fm/modules/update_data/ui/view/interface
mkdir -p src/fm/modules/update_data/ui/view/components/file_pane
mkdir -p src/fm/modules/update_data/ui/events
```

### 1.2 Define Interface Protocol

Create the interface protocol with methods only, following the architectural decision to separate interface methods from events.

**File: `ui/view/interface/i_file_view.py`**

```python
from typing import Protocol, List, Optional

class IFileView(Protocol):
    """Interface for file view component - METHODS ONLY"""
    
    def set_files(self, files: List[str]) -> None:
        """Set the files to display."""
        ...
        
    def add_file(self, file_path: str) -> None:
        """Add a single file to the view."""
        ...
        
    def add_files(self, files: List[str]) -> None:
        """Add multiple files to the view."""
        ...
        
    def remove_file(self, file_path: str) -> None:
        """Remove a file from the view."""
        ...
        
    def get_files(self) -> List[str]:
        """Get the current list of files."""
        ...
        
    def get_selected_file(self) -> Optional[str]:
        """Get the currently selected file."""
        ...
        
    def clear_files(self) -> None:
        """Clear all files from the view."""
        ...
        
    def set_processing_state(self, processing: bool) -> None:
        """Set the processing state of the view."""
        ...
        
    def configure(self, **kwargs) -> None:
        """Configure component behavior."""
        ...
```

### 1.3 Define Events Class

Create the events class with explicit signals for asynchronous communications.

**File: `ui/events/file_events.py`**

```python
from PySide6.QtCore import Signal, QObject
from dataclasses import dataclass
from typing import List, Optional
from datetime import datetime

@dataclass
class FileSelectedEvent:
    """Event data for file selection."""
    file_path: str
    timestamp: datetime = datetime.now()

@dataclass
class FileListChangedEvent:
    """Event data for file list changes."""
    files: List[str]
    source_path: str = ""
    timestamp: datetime = datetime.now()

class FileViewEvents(QObject):
    """Events published by file view component."""
    
    # File selection events
    file_selected = Signal(object)  # FileSelectedEvent
    
    # File list events
    file_list_changed = Signal(object)  # FileListChangedEvent
    
    # User action events
    add_files_requested = Signal()
    remove_file_requested = Signal(str)
```

### 1.4 Create File Model

Define the data model for file information.

**File: `ui/view/components/file_pane/models.py`**

```python
from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional, Dict, Any
from pathlib import Path

@dataclass
class FileInfo:
    """Data model for file information."""
    
    path: str
    name: str = field(init=False)
    folder: str = field(init=False)
    size: int = 0
    created: datetime = None
    modified: datetime = None
    file_type: str = ""
    bank_type: str = ""
    variant: str = ""
    is_valid: bool = True
    is_processed: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Initialize derived fields."""
        path_obj = Path(self.path)
        self.name = path_obj.name
        self.folder = str(path_obj.parent)
        
        # Initialize dates if not provided
        if not self.created:
            self.created = datetime.now()
        if not self.modified:
            self.modified = datetime.now()
```

### 1.5 Create Configuration Class

Define the configuration class for the file view component.

**File: `ui/view/components/file_pane/config.py`**

```python
from dataclasses import dataclass, field
from typing import List

@dataclass
class FileViewConfig:
    """Configuration for file view component."""
    
    # Display options
    show_file_icons: bool = True
    show_file_size: bool = True
    show_file_type: bool = True
    show_folder: bool = True
    
    # File display options
    group_by_folder: bool = True
    sort_by: str = "name"  # "name", "size", "type", "date"
    sort_order: str = "asc"  # "asc", "desc"
    
    # File operations
    allow_add: bool = True
    allow_remove: bool = True
    allow_context_menu: bool = True
    
    # File types
    allowed_file_types: List[str] = field(
        default_factory=lambda: ["*.csv"]  # Default to CSV files only
    )
    
    # Column definitions
    columns: List[str] = field(
        default_factory=lambda: ["name", "file_type", "size", "modified"]
    )
```

### 1.6 Create Component Skeleton

Create the skeleton for the main component.

**File: `ui/view/components/file_pane/ud_file_view.py`**

```python
from PySide6.QtWidgets import QVBoxLayout, QWidget
from fm.gui.components.shared.base_pane import BasePane
from .config import FileViewConfig
from .models import FileInfo
from fm.modules.update_data.ui.events.file_events import FileViewEvents
from typing import List, Optional, Dict, Any
from fm.core.services.logger import log

class UDFileView(BasePane):
    """Self-contained file view component."""
    
    def __init__(self, parent=None):
        """Initialize the component."""
        super().__init__(parent)
        self.setTitle("Files")
        
        # Create events object
        self.events = FileViewEvents()
        
        # Initialize internal state
        self._files: List[FileInfo] = []
        self._selected_file: Optional[FileInfo] = None
        self._config = FileViewConfig()
        
        # Set up UI
        self._setup_ui()
        
    def _setup_ui(self):
        """Set up the component UI."""
        # Main layout
        self._main_layout = QVBoxLayout()
        self._main_layout.setContentsMargins(0, 0, 0, 0)
        self._main_layout.setSpacing(0)
        
        # Set layout to content widget from BasePane
        self.content_widget.setLayout(self._main_layout)
        
        # TODO: Add file table and buttons in Phase 2
        
    # Interface methods (stubs for now)
    
    def set_files(self, files: List[str]) -> None:
        """Set the files to display."""
        log.info(f"UDFileView: Setting {len(files)} files")
        # Implementation in Phase 2
        
    def add_file(self, file_path: str) -> None:
        """Add a single file to the view."""
        log.info(f"UDFileView: Adding file {file_path}")
        # Implementation in Phase 2
        
    def add_files(self, files: List[str]) -> None:
        """Add multiple files to the view."""
        log.info(f"UDFileView: Adding {len(files)} files")
        # Implementation in Phase 2
        
    def remove_file(self, file_path: str) -> None:
        """Remove a file from the view."""
        log.info(f"UDFileView: Removing file {file_path}")
        # Implementation in Phase 2
        
    def get_files(self) -> List[str]:
        """Get the current list of files."""
        return [f.path for f in self._files]
        
    def get_selected_file(self) -> Optional[str]:
        """Get the currently selected file."""
        return self._selected_file.path if self._selected_file else None
        
    def clear_files(self) -> None:
        """Clear all files from the view."""
        log.info("UDFileView: Clearing all files")
        # Implementation in Phase 2
        
    def set_processing_state(self, processing: bool) -> None:
        """Set the processing state of the view."""
        log.info(f"UDFileView: Setting processing state to {processing}")
        # Implementation in Phase 2
        
    def configure(self, **kwargs) -> None:
        """Configure component behavior."""
        # Update configuration
        for key, value in kwargs.items():
            if hasattr(self._config, key):
                setattr(self._config, key, value)
                log.debug(f"UDFileView: Set config {key} = {value}")
        
        # Apply configuration (to be implemented in Phase 2)
```

## Deliverables

1. Directory structure for new component
2. Interface protocol definition
3. Events class definition
4. File model definition
5. Configuration class definition
6. Component skeleton

## Testing

Create basic unit tests to verify:

1. Interface protocol can be implemented
2. Events class signals can be connected
3. File model can be instantiated and populated
4. Configuration class can be instantiated and modified

## Next Steps

Proceed to [Phase 2: Core Component Implementation](./phase2_core_implementation.md) to implement the actual functionality of the file view component.
