"""Configuration keys for Update Data module."""

from enum import Enum
from typing import Any, Dict


class UpdateDataKeys:
    """Configuration keys specific to Update Data module"""
    
    class FolderMonitoring(str, Enum):
        """Folder monitoring settings"""
        KNOWN_FOLDERS = 'update_data.folder_monitoring.known_folders'
        MAX_KNOWN_FOLDERS = 'update_data.folder_monitoring.max_known_folders'
        DEBOUNCE_DELAY = 'update_data.folder_monitoring.debounce_delay'
        ALLOWED_EXTENSIONS = 'update_data.folder_monitoring.allowed_extensions'
    
    class Files(str, Enum):
        """File processing settings"""
        MAX_SIZE = 'update_data.files.max_size'
        ALLOWED_EXTENSIONS = 'update_data.files.allowed_extensions'
        IGNORE_EMPTY = 'update_data.files.ignore_empty'
        SUPPORTED = 'update_data.files.supported_extensions'
        DATA = 'update_data.files.data_extensions'
        CONFIG = 'update_data.files.config_extensions'
        IMAGE = 'update_data.files.image_extensions'
    
    class Source(str, Enum):
        """Source settings"""
        DEFAULT_TYPE = 'update_data.source.default_type'
        RECENT_SOURCES = 'update_data.source.recent_sources'
        LAST_SOURCE_OPTION = 'update_data.source.last_source_option'
    
    class Validation(str, Enum):
        """Validation settings"""
        STRICT_MODE = 'update_data.validation.strict_mode'
    
    class Logging(str, Enum):
        """Logging settings"""
        LOG_PROCESSED = 'update_data.logging.log_processed'
        MAX_RECENT_JOBS = 'update_data.logging.max_recent_jobs'
        # DEBUG_LEVEL removed - debug logging now handled directly in presenter
    
    class History(str, Enum):
        """History tracking"""
        RECENT_MASTERS = 'update_data.history.recent_masters'
        MASTER_HISTORY = 'update_data.history.master_history'
    
    class Paths(str, Enum):
        """Path settings"""
        DATA = 'update_data.paths.data'
        TEMP = 'update_data.paths.temp'
        MASTER = 'update_data.paths.master'
        BACKUP = 'update_data.paths.backup'
        UNRECOGNISED = 'update_data.paths.unrecognised'
        LAST_SOURCE_DIR = 'update_data.paths.last_source_dir'
        LAST_SAVE_DIR = 'update_data.paths.last_save_dir'
    
    class Format(str, Enum):
        """Data format settings"""
        DATE_FORMAT = 'update_data.formats.date_format'
        BANK_TYPE = 'update_data.formats.bank_type'
        FORMAT_TYPE = 'update_data.formats.format_type'
    
    class Bank(str, Enum):
        """Bank type settings"""
        DEFAULT = 'update_data.bank.default_type'
        SUPPORTED = 'update_data.bank.supported_types'
    
    class FormatType(str, Enum):
        """Format type settings"""
        DEFAULT = 'update_data.format.default_type'
        SUPPORTED = 'update_data.format.supported_types'

    class Database(str, Enum):
        """Database update settings"""
        UPDATE_DATABASE = 'update_data.database.update_database'
    
    @classmethod
    def get_defaults(cls) -> Dict[str, Any]:
        """Get default values for all settings."""
        return {
            # File settings
            cls.Files.MAX_SIZE: 100_000,  # 100 KB
            cls.Files.IGNORE_EMPTY: True,
            cls.Files.SUPPORTED: ['.csv', '.xlsx', '.xls', '.ofx', '.pdf'],
            cls.Files.DATA: ['.csv', '.xlsx', '.xls'],
            cls.Files.CONFIG: ['.json', '.yaml', '.yml'],
            cls.Files.IMAGE: ['.png', '.jpg', '.jpeg'],
            
            # Source settings
            cls.Source.DEFAULT_TYPE: 'folder',
            cls.Source.RECENT_SOURCES: [],
            
            # Validation settings
            cls.Validation.STRICT_MODE: True,
            
            # Logging settings
            cls.Logging.LOG_PROCESSED: True,
            cls.Logging.MAX_RECENT_JOBS: 5,
            # DEBUG_LEVEL removed - debug logging now handled directly in presenter
            
            # History
            cls.History.RECENT_MASTERS: [],
            cls.History.MASTER_HISTORY: [],
            
            # Paths
            cls.Paths.DATA: '~/.flatmate/data/update_data',
            cls.Paths.TEMP: '~/.flatmate/temp/update_data',
            cls.Paths.MASTER: '~/.flatmate/data/update_data/master',
            cls.Paths.BACKUP: '~/.flatmate/data/update_data/backup',
            cls.Paths.UNRECOGNISED: '~/.flatmate/data/update_data/unrecognized',
            cls.Paths.LAST_SOURCE_DIR: '~/.flatmate/data/update_data/last_source',
            cls.Paths.LAST_SAVE_DIR: '~/.flatmate/data/update_data/last_save',
            
            # Format settings
            cls.Format.DATE_FORMAT: '%Y-%m-%d',
            cls.Format.BANK_TYPE: 'default',
            cls.Format.FORMAT_TYPE: 'default',
            
            # Bank settings
            cls.Bank.DEFAULT: 'default',
            cls.Bank.SUPPORTED: ['default', 'ASB', 'westpac', 'anz'],
            
            # Format type settings
            cls.FormatType.DEFAULT: 'default',
            cls.FormatType.SUPPORTED: ['default', 'custom'],

            # Database settings
            cls.Database.UPDATE_DATABASE: True,
            
            # Folder monitoring settings
            cls.FolderMonitoring.KNOWN_FOLDERS: [],
            cls.FolderMonitoring.MAX_KNOWN_FOLDERS: 5,
            cls.FolderMonitoring.DEBOUNCE_DELAY: 2.0,
            cls.FolderMonitoring.ALLOWED_EXTENSIONS: ['.csv']
        }
